import {Linking, Alert, Platform} from 'react-native';
import {userInfoRequest} from '../actions/UserActions';
import DataHandler from './DataHandler';

export interface YotiDeepLinkParams {
  sessionId?: string;
  status?: 'success' | 'failure' | 'cancelled';
  error?: string;
}

class YotiService {
  private static instance: YotiService;
  private isProcessingDeepLink = false;

  static getInstance(): YotiService {
    if (!YotiService.instance) {
      YotiService.instance = new YotiService();
    }
    return YotiService.instance;
  }

  /**
   * Initialize deep link listener for Yoti app returns
   */
  initializeDeepLinkListener() {
    // Listen for deep links when app is already open
    const linkingListener = Linking.addEventListener(
      'url',
      this.handleDeepLink,
    );

    // Handle deep link when app is opened from closed state
    Linking.getInitialURL().then(url => {
      if (url) {
        this.handleDeepLink({url});
      }
    });

    return () => {
      linkingListener?.remove();
    };
  }

  /**
   * Handle incoming deep link from Yoti app
   */
  private handleDeepLink = async ({url}: {url: string}) => {
    if (!url || this.isProcessingDeepLink) return;

    // Check if this is a Yoti return URL
    if (url.includes('yoti-return') || url.includes('age-verification')) {
      this.isProcessingDeepLink = true;

      try {
        console.log('Yoti deep link received:', url);

        // Parse URL parameters
        const params = this.parseDeepLinkParams(url);

        // Refresh user info to get updated ageVerified status
        await this.refreshUserInfo();

        // Handle the result
        this.handleYotiResult(params);
      } catch (error) {
        console.error('Error handling Yoti deep link:', error);
        Alert.alert(
          'Verification Error',
          'Failed to process age verification result.',
        );
      } finally {
        this.isProcessingDeepLink = false;
      }
    }
  };

  /**
   * Parse deep link parameters
   */
  private parseDeepLinkParams(url: string): YotiDeepLinkParams {
    const urlObj = new URL(url);
    const params: YotiDeepLinkParams = {};

    urlObj.searchParams.forEach((value, key) => {
      switch (key) {
        case 'sessionId':
          params.sessionId = value;
          break;
        case 'status':
          params.status = value as 'success' | 'failure' | 'cancelled';
          break;
        case 'error':
          params.error = value;
          break;
      }
    });

    return params;
  }

  /**
   * Refresh user info to get updated ageVerified status
   */
  private async refreshUserInfo(): Promise<any> {
    return new Promise((resolve, reject) => {
      const store = DataHandler.getStore();
      if (!store) {
        reject(new Error('Store not available'));
        return;
      }

      // Dispatch userInfoRequest action
      store.dispatch(
        userInfoRequest((response: any) => {
          if (response) {
            console.log(
              'User info refreshed after Yoti verification:',
              response.ageVerified,
            );
            resolve(response);
          } else {
            reject(new Error('Failed to refresh user info'));
          }
        }) as any,
      );
    });
  }

  /**
   * Handle Yoti verification result
   */
  private handleYotiResult(params: YotiDeepLinkParams) {
    const {status, error} = params;

    switch (status) {
      case 'success':
        Alert.alert(
          'Age Verification Successful',
          'Your age has been verified successfully.',
          [{text: 'OK', onPress: () => this.navigateAfterVerification()}],
        );
        break;

      case 'failure':
        Alert.alert(
          'Age Verification Failed',
          error || 'Age verification failed. Please try again.',
          [
            {text: 'Retry', onPress: () => this.retryVerification()},
            {text: 'Cancel', style: 'cancel'},
          ],
        );
        break;

      case 'cancelled':
        Alert.alert(
          'Age Verification Cancelled',
          'Age verification was cancelled. You can retry from your profile.',
          [{text: 'OK', style: 'cancel'}],
        );
        break;

      default:
        // If no specific status, check user info for ageVerified
        const userState = DataHandler.getStore()?.getState()?.user;
        if (userState?.userInfo?.ageVerified) {
          this.navigateAfterVerification();
        }
        break;
    }
  }

  /**
   * Navigate to appropriate screen after successful verification
   */
  private navigateAfterVerification() {
    // This will be handled by the splash screen or current navigation logic
    // The updated userInfo will trigger the appropriate navigation
    console.log(
      'Age verification completed, navigation will be handled by current flow',
    );
  }

  /**
   * Retry age verification
   */
  private retryVerification() {
    // Trigger Yoti verification again
    const store = DataHandler.getStore();
    if (store) {
      // This would trigger the Yoti flow again
      console.log('Retrying age verification...');
    }
  }

  /**
   * Open Yoti app for age verification
   */
  async openYotiApp(sessionUrl: string, fallbackUrl: string): Promise<boolean> {
    try {
      // Try to open Yoti app directly
      const yotiAppUrl = `yoti://verify?session=${encodeURIComponent(
        sessionUrl,
      )}`;
      const canOpen = await Linking.openURL(fallbackUrl);

      if (canOpen) {
        await Linking.openURL(fallbackUrl);
        return true;
      } else {
        // Fallback to browser if Yoti app is not installed
        if (fallbackUrl) {
          await Linking.openURL(fallbackUrl);
          return true;
        } else {
          Alert.alert(
            'Yoti App Required',
            'Please install the Yoti app to complete age verification.',
            [
              {
                text: 'Install Yoti',
                onPress: () => this.openYotiAppStore(),
              },
              {text: 'Cancel', style: 'cancel'},
            ],
          );
          return false;
        }
      }
    } catch (error) {
      console.error('Error opening Yoti app:', error);
      Alert.alert(
        'Error',
        'Failed to open age verification. Please try again.',
      );
      return false;
    }
  }

  /**
   * Open app store to install Yoti app
   */
  private async openYotiAppStore() {
    const appStoreUrl = 'https://apps.apple.com/app/yoti/id983980808';
    const playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.yoti.mobile.android.live';

    try {
      const url = Platform.OS === 'ios' ? appStoreUrl : playStoreUrl;
      await Linking.openURL(url);
    } catch (error) {
      console.error('Error opening app store:', error);
    }
  }

  /**
   * Check if user needs age verification
   */
  static needsAgeVerification(userInfo: any): boolean {
    return userInfo && userInfo.ageVerified === false;
  }
}

export default YotiService;
