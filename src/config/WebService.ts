import _ from 'lodash';
import Util from '../util';
import {APIConstants} from '../constants/APIConstants';

export const BASE_URL = 'https://sigma.imprint.live/backend';
export const RUDI_API_URL = BASE_URL.includes('sigma')
  ? 'https://rudi-func-qa.azurewebsites.net/api/chat-service'
  : 'https://rudi-func-stage.azurewebsites.net/api/chat-service';

export const API_TIMEOUT = 3000000;
// export const NEW_API_KEY = '1d399038bef14b0497d028fc27999696';

// API USER ROUTES
export const API_LOG = true;

export const ERROR_SOMETHING_WENT_WRONG = {
  message: 'Something went wrong, Please try again later',
  error: 'Something went wrong, Please try again later',
};
export const ERROR_NETWORK_NOT_AVAILABLE = {
  message: 'Please connect to the working Internet',
  error: 'Please connect to the working Internet',
};

export const ERROR_CANCEL_ERROR = {
  message: 'cancel Error',
  error: 'Something went wrong, Please try again later',
};

export const ERROR_TOKEN_EXPIRE = {
  message: 'Session Expired, Please login again!',
  error: 'Session Expired, Please login again!',
};

export const REQUEST_TYPE = {
  GET: 'get',
  POST: 'post',
  DELETE: 'delete',
  PUT: 'put',
  PATCH: 'patch',
};

// API USER ROUTES
export const EMPTY = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};
export const FREE_TEXT_SEARCH_IMPRINTS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const USER_SIGNUP = {
  route: APIConstants.USER_SIGNUP,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const ACCEPT_TERMS_CONDITIONS = {
  route: APIConstants.ACCEPT_TERMS_CONDITIONS,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const GET_PLANS = {
  route: APIConstants.GET_PLANS,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_PRO_PLANS = {
  route: APIConstants.GET_PRO_PLANS,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const VALIDATE_INVITE_CODE = {
  route: APIConstants.VALIDATE_INVITE_CODE,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const VALIDATE_RECEIPT = {
  route: APIConstants.VALIDATE_RECEIPT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const PURCHASE_SUBSCRIPTIONS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const VALID_COUPON = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const SEARCH_USER = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};
export const GET_ENTERTAINMENTS_CONTENT = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const SHARE_IMPRINT = {
  route: APIConstants.SHARE_IMPRINT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const GET_ZEN_DESK_TOKEN = {
  route: APIConstants.GET_ZEN_DESK_TOKEN,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const QUIZ_PREVIEW_LIST = {
  route: APIConstants.QUIZ_PREVIEW_LIST,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const ENTERTAINMENT_QUIZ = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const ENTERTAINMENT_QUIZ_ATTEMPT = {
  route: APIConstants.ENTERTAINMENT_QUIZ_ATTEMPT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const USER_SIGN_IN = {
  route: APIConstants.USER_SIGN_IN,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const SEND_OTP = {
  route: APIConstants.SEND_OTP,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const VALIDATE_OTP = {
  route: APIConstants.VALIDATE_OTP,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const RESET_PASSWORD = {
  route: APIConstants.RESET_PASSWORD,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const USER_POST_IMPRINT = {
  route: APIConstants.USER_POST_IMPRINT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const GET_VALUE_ANALYTICS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_VALUE_PORTFOLIO = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const PROFILE_QUIZ = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};
export const SUBMIT_PROFILE_QUIZ = {
  route: APIConstants.SUBMIT_PROFILE_QUIZ,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const BILLING_PORTAL = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_TIME_OPTIONS = {
  route: APIConstants.TIME_OPTION,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const USER_UPDATE_IMPRINT = {
  route: APIConstants.USER_POST_IMPRINT,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const USER_FRIENDS_SUGGESTION = {
  route: APIConstants.USER_FRIENDS_SUGGESTION,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};
export const FOLLOW_FRIENDS = {
  route: APIConstants.FOLLOW_FRIENDS,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const UN_FOLLOW = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};
export const GET_CONVERSATION_MESSAGES = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const USER_FOLLOWERS = {
  route: APIConstants.USER_FOLLOWERS,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_NOTIFICATIONS = {
  route: APIConstants.NOTIFICATION,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_RUDI_MESSAGE = {
  route: APIConstants.RUDI_MESSAGE,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_RUDI_FILE = {
  route: APIConstants.RUDI_FILE,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const SEND_RUDI_MESSAGE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const UPLOAD_RUDI_FILE = {
  route: APIConstants.RUDI_FILE,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const DELETE_RUDI_FILE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const UPDATE_VERIFICATION = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PATCH,
};

export const READ_NOTIFICATION = {
  route: APIConstants.READ_NOTIFICATION,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const GET_FRIEND_REQUEST_SENDER = {
  route: APIConstants.GET_FRIEND_REQUEST_SENDER,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const MEDIA_CHECK_IN_VIEW = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const TAG_MEDIA = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const RESPOND_TO_TAG = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const RESPONSE_TO_FOLLOWERS = {
  route: APIConstants.RESPONSE_TO_FOLLOWERS,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const VERIFY_USERNAME = {
  route: APIConstants.VERIFY_USERNAME,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const SETUP_PROFILE = {
  route: APIConstants.SETUP_PROFILE,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const EDIT_IMAGE = {
  route: APIConstants.EDIT_IMAGE,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const UPDATE_PROFILE_OVERVIEW = {
  route: APIConstants.UPDATE_OVERVIEW,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const DELETE_EDUCATION = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const DELETE_FAMILY = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const DELETE_EMPLOYMENT = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const UPDATE_EDUCATION = {
  route: APIConstants.UPDATE_EDUCATION,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const UPDATE_EMPLOYMENT = {
  route: APIConstants.UPDATE_EMPLOYMENT,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const ADD_EDUCATION = {
  route: APIConstants.UPDATE_EDUCATION,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const UPDATE_FAMILY = {
  route: APIConstants.ADD_RELATION,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const GET_OTHER_USER_DETAILS = {
  route: APIConstants.OTHER_USER_DETAILS,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const ADD_EMPLOYMENT = {
  route: APIConstants.UPDATE_EMPLOYMENT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const ADD_FAMILY = {
  route: APIConstants.ADD_RELATION,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const UPDATE_PROFILE_ABOUT = {
  route: APIConstants.UPDATE_ABOUT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const MODERATION_PROFILE = {
  route: APIConstants.MODERATION_PROFILE,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const MODERATION_PROFILE_PHOTO = {
  route: APIConstants.MODERATION_PROFILE_PHOTO,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const GET_TRANSPARENCY_DATA = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const UPDATE_CONTACT = {
  route: APIConstants.CONTACT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const REPORT_CONTENT = {
  route: APIConstants.REPORT_CONTENT,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const VIOLATION_POLICIES = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_INSTITUTE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_USERS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_RELATION_ENUMS = {
  route: APIConstants.RELATIONSHIP_ENUM,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const COMPLETE_PROFILE = {
  route: APIConstants.COMPLETE_PROFILE,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const USER_INFO = {
  route: APIConstants.USER_INFO,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GOOGLE_SIGN_IN = {
  route: APIConstants.GOOGLE_SIGN_IN,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const APPLE_SIGN_IN = {
  route: APIConstants.APPLE_SIGN_IN,
  access_token_required: false,
  type: REQUEST_TYPE.POST,
};

export const GLOBAL_TIMELINE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const DELETE_IMPRINT = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const DELETE_CHAT = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const DELETE_MESSAGE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const GET_IMPRINT_BY_ID = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const PERSONAL_TIMELINE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const USER_TIME_LINE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const FREE_TEXT_SEARCH_USERS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_NEXT_OF_KIN = {
  route: APIConstants.NEXT_OF_KIN,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_SUBSCRIPTION_PLAN = {
  route: APIConstants.SUBSCRIPTION,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_SUSBSCRIPTION_PACKAGE_FEATURES = {
  route: APIConstants.SUBSCRIPTION_PACKAGE_FEATURES,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_USER_TIMELINE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_FILTERED_TIMELINE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const DELETE_NEXT_OF_KIN = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const POST_REACTION = {
  route: APIConstants.POST_REACTION,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const FOLLOW_REQUEST = {
  route: APIConstants.RESPONSE_TO_FOLLOWERS,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const ADD_NEXT_OF_KIN = {
  route: APIConstants.NEXT_OF_KIN,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const REMIND_NEXT_OF_KIN = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PATCH,
};

export const IMPRINT_VERIFICATION_REQUEST = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const BLOCK_IMPRINT = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const BLOCK_USER = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const BLOCK_USERS_LISTING = {
  route: APIConstants.BLOCK_USERS_LISTING,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const FRIENDS_SUGGESTIONS = {
  route: APIConstants.FRIENDS_SUGGESTIONS,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const BLOCK_MESSAGE = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const POST_SHOUT_OUT = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PATCH,
};

export const IMPRINT_BOOKMARK = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

//Chat Routes
export const RECENT_CHAT_LIST = {
  route: APIConstants.RECENT_CHAT_LIST,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_NEWS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const CREATE_CHAT_GROUP = {
  route: APIConstants.CREATE_CHAT_GROUP,
  access_token_required: true,
  type: REQUEST_TYPE.POST,
};

export const DELETE_CHAT_GROUP = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.DELETE,
};

export const LEAVE_CONVERSATION = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PATCH,
};

export const UPDATE_READ_STATUS = {
  route: '',
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const REMIND_LATER = {
  route: APIConstants.REMIND_LATER,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const GET_PUB_SUB_TOKEN = {
  route: APIConstants.GET_PUB_SUB_TOKEN,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const APPEAL_DECISION = {
  route: APIConstants.APPEAL_DECISION,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const GET_CHAT_TOKEN = {
  route: APIConstants.GET_CHAT_TOKEN,
  access_token_required: true,
  type: REQUEST_TYPE.GET,
};

export const FLAG_MESSAGE = {
  route: APIConstants.FLAG_MESSAGE,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const FLAG_CONVERSATION = {
  route: APIConstants.FLAG_CONVERSATION,
  access_token_required: true,
  type: REQUEST_TYPE.PUT,
};

export const callRequest = function (
  url: any,
  data: any,
  parameter: any,
  header = {},
  ApiSauce: any,
  baseUrl = BASE_URL,
) {
  // note, import of "ApiSause" has some errors, that's why I am passing it through parameters

  let _header = {Origin: baseUrl};
  if (url.access_token_required) {
    const _access_token = Util.getCurrentUserAccessToken();
    if (_access_token) {
      _header.Authorization = `Bearer ${_access_token}`;
    }
  }

  // Merge custom headers passed via arguments
  _header = {..._header, ...header};

  const _url =
    parameter && !_.isEmpty(parameter)
      ? `${url.route}/${parameter}`
      : url.route;

  if (url.type === REQUEST_TYPE.POST) {
    return ApiSauce.post(_url, data, _header, baseUrl);
  } else if (url.type === REQUEST_TYPE.GET) {
    return ApiSauce.get(_url, data, _header, baseUrl);
  } else if (url.type === REQUEST_TYPE.PUT) {
    return ApiSauce.put(_url, data, _header, baseUrl);
  } else if (url.type === REQUEST_TYPE.DELETE) {
    return ApiSauce.delete(_url, data, _header, baseUrl);
  } else if (url.type === REQUEST_TYPE.PATCH) {
    return ApiSauce.patch(_url, data, _header, baseUrl);
  }
};
