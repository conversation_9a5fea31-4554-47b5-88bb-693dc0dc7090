import {Platform, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

// @flow

export default StyleSheet.create({
  container: {
    flex: 1,
    borderTopLeftRadius: Metrics.ratio(20),
    borderTopRightRadius: Metrics.ratio(20),
    borderBottomLeftRadius: Metrics.ratio(20),

    backgroundColor: Colors.background.home,
  },
  rudiChatContainer: {
    height: Metrics.ratio(40),
    width: Metrics.ratio(40),
    borderRadius: Metrics.ratio(30),
    backgroundColor: Colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },

  seperator: {
    height: Metrics.ratio(1),
    backgroundColor: Colors.background.publishInactiveColor,
  },
  textInputProps: {
    marginRight: Metrics.ratio(100),
    left: Metrics.ratio(40),
    justifyContent: 'space-between',
    fontSize: Fonts.size.xSmall,
    backgroundColor: Colors.white,
    color: Colors.text.placeHolderTextColor,
    paddingTop: Metrics.ratio(5),
    paddingBottom: Metrics.ratio(12),
    paddingHorizontal: Metrics.ratio(20),
    minHeight: Metrics.ratio(44),
    maxHeight: Metrics.ratio(100),
    borderRadius: Metrics.ratio(18),
    borderWidth: Metrics.ratio(1.5),
    borderColor: Colors.textInputBorder,
    alignSelf: 'center',
  },

  textLeftStyle: {
    maxWidth: Metrics.screenWidth * 0.6,
    backgroundColor: Colors.black,
    color: Colors.white,
  },
  textRightStyle: {
    color: Colors.toggleColors.activeColor,
    marginHorizontal: Metrics.baseMargin,
  },
  combineTextStyle: {
    overflow: 'hidden',
    paddingHorizontal: Metrics.ratio(10),
  },
  chatBubbleRightStyle: {
    marginBottom: Metrics.ratio(10),
    backgroundColor: Colors.white,
    borderWidth: Metrics.ratio(0),
    borderColor: 'transparent',
    borderTopLeftRadius: Metrics.ratio(10),
    borderTopRightRadius: Metrics.ratio(6),
    borderBottomRightRadius: Metrics.ratio(6),
    borderBottomLeftRadius: Metrics.ratio(10),
    minWidth: Metrics.ratio(80),
    maxWidth: Metrics.screenWidth * 0.7,
  },
  chatBubbleLeftStyle: {
    overflow: 'hidden',
    marginBottom: Metrics.ratio(10),
    backgroundColor: Colors.black,
    borderWidth: Metrics.ratio(0),
    borderColor: 'transparent',
    borderTopLeftRadius: Metrics.ratio(6),
    borderTopRightRadius: Metrics.ratio(10),
    borderBottomRightRadius: Metrics.ratio(10),
    borderBottomLeftRadius: Metrics.ratio(6),
    minWidth: Metrics.ratio(100),
    maxWidth: Metrics.screenWidth * 0.7,
  },
  timeText: {
    color: Colors.text.placeHolderTextColor,
  },
  sendIcon: {
    backgroundColor: Colors.white,
    height: Metrics.ratio(40),
    width: Metrics.ratio(40),
    borderRadius: Metrics.ratio(20),
    marginHorizontal: Metrics.ratio(15),
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    position: 'absolute',
    right: 0,
  },
  inputContainer: {
    justifyContent: 'center',
    maxHeight: Metrics.ratio(200),
    height: Metrics.ratio(108),
    backgroundColor: Colors.background.home,
    borderColor: Colors.background.home,
  },

  navBar: {
    backgroundColor: Colors.background.home,
  },
  statusBar: {
    backgroundColor: Colors.background.home,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.background.home,
    height: Metrics.ratio(30),
  },
  AttachmentContainer: {
    alignSelf: 'flex-end',
    backgroundColor: Colors.white,
    top: Metrics.ratio(120),
    position: 'absolute',
    zIndex: 999,
    height: Metrics.ratio(50),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(150),
    right: Metrics.ratio(20),
  },
  extraHeight: {height: 150},
  mHorizontal: {marginHorizontal: Metrics.ratio(10)},
  downLoadButton: {flexDirection: 'row', marginVertical: Metrics.ratio(20)},
  overlay: {
    position: 'absolute',
    height: Metrics.ratio(50),
    width: Metrics.ratio(60),
    right: 0,
    top: Platform.OS === 'android' ? Metrics.ratio(-90) : Metrics.ratio(-50),
  },
  footerComponent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: Metrics.ratio(10),
  },
  rudiText: {
    fontStyle: 'normal',
    color: Colors.black2,
    fontSize: Fonts.size.small,
  },
  titleWithImage: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  relatedArticleText: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(16),
    color: Colors.black,
    paddingLeft: Metrics.ratio(12),
  },
  imageContainer: {
    position: 'absolute',
    right: Metrics.ratio(16),
  },
  articleContainer: {
    paddingHorizontal: Metrics.ratio(12),
    backgroundColor: Colors.background.article,
  },
  articleTitle: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: Metrics.ratio(14),
    color: Colors.text.titleColor,
    paddingHorizontal: Metrics.ratio(12),
    paddingTop: Metrics.ratio(12),
    paddingBottom: Metrics.ratio(5),
    width: Metrics.screenWidth * 0.5,
  },
  articleDescription: {
    fontFamily: Fonts.type.regular,
    fontSize: Metrics.ratio(14),
    color: Colors.textLight,
    paddingHorizontal: Metrics.ratio(12),
    width: Metrics.screenWidth * 0.4,
  },
  textContainer: {
    backgroundColor: Colors.white,
    paddingBottom: Metrics.ratio(10),
    borderBottomLeftRadius: Metrics.ratio(8),
    borderBottomRightRadius: Metrics.ratio(8),
    marginBottom: Metrics.ratio(24),
  },
  articleImage: {
    flex: 1,
    height: Metrics.ratio(200),
    backgroundColor: Colors.white,
    borderTopLeftRadius: Metrics.ratio(8),
    borderTopRightRadius: Metrics.ratio(8),
  },
  expandedIcon: {
    paddingRight: Metrics.ratio(10),
    alignItems: 'center',
    backgroundColor: Colors.black,
    flexDirection: 'row',
    borderRadius: Metrics.ratio(8),
    width: Metrics.ratio(120),
    justifyContent: 'center',
  },
  expandText: {
    fontSize: Metrics.ratio(14),
    color: Colors.white,
    paddingRight: Metrics.ratio(5),
  },
});
