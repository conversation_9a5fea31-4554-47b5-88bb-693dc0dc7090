import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  PermissionsAndroid,
  Platform,
  Keyboard,
  Image,
  Alert,
  Linking,
  FlatList,
  Pressable,
} from 'react-native';
import styles from './styles.ts';
import {
  ATTACHMENT_RUDI,
  CHANGE_FILE,
  DELETE_FILE,
  MINUS_ICON,
  PLUS_ICON,
  RUDI_PROFILE_ICON,
  SEND_BUTTON,
  THREE_DOT_VERTICAL,
  VIEW_FILE,
} from '../../constants/AssetSVGConstants.ts';
import CustomNavbar from '../CustomNavbar/index.tsx';
import {connect} from 'react-redux';
import {
  getRudiMessagesRequest,
  getRudiFileRequest,
  sendRudiMessageRequest,
  uploadRudiFileRequest,
  deleteRudiFileRequest,
} from '../../actions/RudiActions.ts';
import {
  Message,
  RudiFileType,
  RudiMessageType,
  UserInfo,
} from '../../types/index.tsx';
import {
  Bubble,
  B<PERSON>bleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
  Time,
} from 'react-native-gifted-chat';
import util from '../../util/index.tsx';
import Colors from '../../theme/Colors.ts';
import RNFS from 'react-native-fs';
import * as DocumentPicker from 'react-native-document-picker';
import Share from 'react-native-share';
import {showToastMsg} from '../Alert/index.tsx';
import {
  useFocusEffect,
  useIsFocused,
  useNavigation,
} from '@react-navigation/native';
import {
  CHAT_LIST,
  COMMON,
  HOME,
  RUDI_MESSAGE,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants.ts';
import {hasNotch} from 'react-native-device-info';
import {ButtonView, Text, Loader} from '../../components';
import AppStyles from '../../theme/AppStyles.ts';
import OutsidePressHandler from 'react-native-outside-press';
import TypingIndicator from 'react-native-gifted-chat/lib/TypingIndicator';
import _ from 'lodash';
import {AvoidSoftInput} from 'react-native-avoid-softinput';
import DefaultImage from '../../assets/icons/rudi.png';
import {DATE_FORMAT_8} from '../../constants/index.ts';
import Metrics from '../../theme/Metrics.ts';

interface RudiMessageProps {
  getRudiMessagesRequest: (payload: any, callback: (res: any) => void) => void;
  deleteRudiFileRequest: (payload: any, callback: (res: any) => void) => void;

  getRudiFileRequest: (callback: (res: any) => void) => void;
  sendRudiMessageRequest: (payload: any, callback: (res: any) => void) => void;
  uploadRudiFileRequest: (payload: any, callback: (res: any) => void) => void;
  rudiMessages: RudiMessageType;
  user: UserInfo;

  route?: {
    params?: {
      dailyNews: any[];
      dailyNewsFlag: boolean;
      customPrompt: string;
    };
  };
}

const RudiChat: React.FC<RudiMessageProps> = ({
  getRudiMessagesRequest,
  deleteRudiFileRequest,
  getRudiFileRequest,
  sendRudiMessageRequest,
  uploadRudiFileRequest,

  rudiMessages,
  user,
  route,
}) => {
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [rudiFile, setRudiFile] = useState<RudiFileType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const hasSentInitialMessage = useRef(false);

  const [showAttachmentView, setAttachmentView] = useState<boolean>(false);

  const [newsData] = useState<any>(route?.params?.dailyNews ?? []);
  const [text, setText] = useState<string | undefined>(
    route?.params?.customPrompt
      ? ` ${route?.params?.customPrompt}..\n${newsData[0]?.summary}`
      : undefined,
  );
  const [page, setPage] = useState<number>(1);
  const isFocused = useIsFocused();
  const navigation = useNavigation();

  useEffect(() => {
    if (!isFocused) {
      setPage(1);
    }
  }, [isFocused]);

  useFocusEffect(
    useCallback(() => {
      if (Platform.OS === 'android') {
        AvoidSoftInput?.setAdjustResize();
      }
      return () => {
        if (Platform.OS === 'android') {
          AvoidSoftInput.setAdjustPan();
        }
      };
    }, []),
  );

  useEffect(() => {
    if (
      route?.params?.customPrompt &&
      newsData.length > 0 &&
      !hasSentInitialMessage.current
    ) {
      setMainLoading(true);
      const initialMessageText = `${route.params.customPrompt}\n${newsData[0]?.summary}`;
      const userMessage = {
        _id: Math.random().toString(),
        text: initialMessageText,
        createdAt: new Date(),
        user: {
          _id: user.userId,
          name: user.displayName,
          avatar: user.avatarUrl,
        },
      };

      setTimeout(() => {
        onSend([userMessage], route?.params?.dailyNewsFlag ?? false);
        setMainLoading(false);
      }, 2000);

      hasSentInitialMessage.current = true;
      navigation.setParams({customPrompt: undefined});
    }
  }, [route?.params?.customPrompt, newsData]);

  useEffect(() => {
    const payLoad = {
      page: page,
    };
    getRudiMessagesRequest(payLoad, (res: RudiMessageType) => {
      if (res) {
        if (page > 1) {
          const updatedResponse: IMessage[] = util.transformRudiMessages(
            res.messages,
            user,
          );
          setMessages([...messages, ...updatedResponse]);
        } else {
          const updatedResponse: IMessage[] = util.transformRudiMessages(
            res.messages,
            user,
          );
          setMessages(updatedResponse);
        }
      }
    });
    if (rudiFile === undefined) {
      getRudiFileRequest((res: RudiFileType) => {
        if (res.fileUrl) {
          setRudiFile(res);
        }
      });
    }
  }, [isFocused, page, route?.params?.customPrompt]);

  const onPressArticle = (item: any) => {
    Alert.alert(HOME.DISCLAIMER, HOME.DISCLAIMER_TEXT, [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Redirect',
        onPress: () => Linking.openURL(item.url ?? ''),
      },
    ]);
  };

  const RenderArticleItem = React.memo(
    ({item, onPress}: {item: any; onPress: () => void}) => (
      <View style={styles.articleContainer}>
        <Pressable onPress={onPress} style={{flex: 1}}>
          <Text style={styles.articleTitle} numberOfLines={1}>
            {util.getFormattedDateTime(item.datePublished, DATE_FORMAT_8)}
          </Text>
          <Image
            source={{uri: item.imageUrl}}
            defaultSource={require('../../assets/icons/attachment.png')}
            style={styles.articleImage}
            resizeMode="center"
          />
        </Pressable>
        <View style={styles.textContainer}>
          <Text style={styles.articleTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.articleDescription} numberOfLines={1}>
            {item.summary}
          </Text>
        </View>
      </View>
    ),
  );

  const renderBubble = (props: any) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
      <View>
        <Bubble
          {...props}
          textStyle={{
            left: styles.textLeftStyle,
            right: styles.textRightStyle,
          }}
          customTextStyle={styles.combineTextStyle}
          wrapperStyle={{
            right: styles.chatBubbleRightStyle,
            left: styles.chatBubbleLeftStyle,
          }}
        />
        {props.currentMessage?.news_articles?.length > 0 && (
          <>
            <ButtonView
              onPress={() => setIsExpanded(!isExpanded)}
              style={AppStyles.mBottom15}>
              {isExpanded ? (
                <View style={styles.expandedIcon}>
                  <Text style={styles.expandText}>Collapse</Text>
                  <MINUS_ICON height={20} width={20} />
                </View>
              ) : (
                <View style={styles.expandedIcon}>
                  <Text style={styles.expandText}>Expand</Text>
                  <PLUS_ICON height={10} width={10} />
                </View>
              )}
            </ButtonView>
            {isExpanded === true && (
              <FlatList
                style={{
                  marginRight: Metrics.ratio(50),
                  backgroundColor: Colors.background.home,
                }}
                horizontal
                data={props.currentMessage.news_articles}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({item}) => (
                  <RenderArticleItem
                    item={item}
                    onPress={() => onPressArticle(item)}
                  />
                )}
              />
            )}
          </>
        )}
      </View>
    );
  };

  const onDeleteFile = () => {
    const payload = {
      id: rudiFile?.id,
    };

    deleteRudiFileRequest(payload, (res: any) => {
      if (res) {
        setRudiFile(undefined);
        showToastMsg(TOAST_MESSAGES.FILE_DELETED, TOAST_TYPE.SUCCESS);
      }
    });
  };

  const renderAttachmentView = () => {
    return (
      <OutsidePressHandler
        style={[styles.AttachmentContainer, rudiFile && styles.extraHeight]}
        onOutsidePress={() => setAttachmentView(false)}>
        <ButtonView
          onPress={() => setAttachmentView(false)}
          style={styles.overlay}
        />

        {rudiFile === undefined ? (
          <ButtonView
            style={AppStyles.flexRow}
            onPress={() => {
              pickAndUploadFile();
              setAttachmentView(false);
            }}>
            <ATTACHMENT_RUDI />
            <Text color={'#000'} style={styles.mHorizontal} size="xxSmall">
              {RUDI_MESSAGE.UPLOAD}
            </Text>
          </ButtonView>
        ) : (
          <View>
            <ButtonView
              style={[AppStyles.flexRow, AppStyles.alignItemsCenter]}
              onPress={() => {
                pickAndUploadFile();
                setAttachmentView(false);
              }}>
              <CHANGE_FILE />
              <Text color={'#000'} style={AppStyles.mLeft5} size="xxSmall">
                {RUDI_MESSAGE.CHANGE_FILE}
              </Text>
            </ButtonView>

            <ButtonView
              style={[styles.downLoadButton, AppStyles.alignItemsCenter]}
              onPress={() => {
                downloadFile();
                setAttachmentView(false);
              }}>
              <VIEW_FILE />
              <Text
                style={styles.mHorizontal}
                color={Colors.black}
                size="xxSmall">
                {RUDI_MESSAGE.VIEW_FILE}
              </Text>
            </ButtonView>

            <ButtonView
              style={[AppStyles.flexRow, AppStyles.alignItemsCenter]}
              onPress={() => {
                Alert.alert(
                  RUDI_MESSAGE.DELETE_FILE,
                  RUDI_MESSAGE.DELETE_FILE_CONFIRMATION,
                  [
                    {
                      text: COMMON.CANCEL,
                      onPress: () => {},
                      style: 'cancel',
                    },
                    {
                      text: COMMON.OK,

                      onPress: () => {
                        onDeleteFile();
                        setAttachmentView(false);
                      },
                    },
                  ],
                );
              }}>
              <DELETE_FILE />
              <Text
                style={styles.mHorizontal}
                color={Colors.black}
                size="xxSmall">
                {RUDI_MESSAGE.DELETE_FILE}
              </Text>
            </ButtonView>
          </View>
        )}
      </OutsidePressHandler>
    );
  };

  const renderTime = (props: any) => {
    return (
      <Time
        {...props}
        timeTextStyle={{
          left: styles.timeText,
          right: styles.timeText,
        }}
      />
    );
  };

  const checkPermission = async () => {
    if (Platform.OS === 'ios') {
      return true;
    }
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      {
        title: RUDI_MESSAGE.PERMISSION_TITLE,
        message: RUDI_MESSAGE.PERMISSION_MESSAGE,
        buttonPositive: RUDI_MESSAGE.PERMISSION_BUTTON,
      },
    );
    return granted === PermissionsAndroid.RESULTS.GRANTED;
  };

  const getDownloadDirectoryPath = () => {
    if (Platform.OS === 'ios') {
      return RNFS.DocumentDirectoryPath;
    } else {
      return RNFS.DownloadDirectoryPath || RNFS.DocumentDirectoryPath;
    }
  };

  const downloadFile = async () => {
    const url = rudiFile?.fileUrl ?? '';
    const downloadDir = getDownloadDirectoryPath();
    const destination = `${downloadDir}/${rudiFile?.fileName}`;
    if (await checkPermission()) {
      RNFS.downloadFile({
        fromUrl: url,
        toFile: destination,
      })
        .promise.then(res => {
          if (res.statusCode === 200) {
            saveToFile(destination);
          } else {
            showToastMsg(RUDI_MESSAGE.ERROR_DOWNLOADING);
          }
        })
        .catch(error => {
          showToastMsg(RUDI_MESSAGE.ERROR_DOWNLOADING);
        });
    } else {
      Alert.alert(
        TOAST_MESSAGES.PERMISSION_REQUIRED,
        TOAST_MESSAGES.PERMISSION_REQUIRED_DETAIL,
        [
          {text: 'Open Setting', onPress: () => Linking.openSettings()},
          {text: 'Not Now', onPress: () => {}},
        ],
      );
    }
  };

  const saveToFile = (filePath: string) => {
    const shareOptions = {
      title: RUDI_MESSAGE.SAVE_FILE_TO,
      url: `file://${filePath}`,
      type: rudiFile?.fileType,
      saveToFiles: true,
    };
    Share.open(shareOptions);
  };

  const renderSend = (props: any) => {
    return (
      <Send {...props} containerStyle={styles.sendIcon}>
        <SEND_BUTTON height={18} width={18} />
      </Send>
    );
  };

  const pickAndUploadFile = () => {
    DocumentPicker.pick({
      type: [
        DocumentPicker.types.doc,
        DocumentPicker.types.docx,
        DocumentPicker.types.pdf,
        DocumentPicker.types.plainText,
        DocumentPicker.types.ppt,
        DocumentPicker.types.pptx,
      ],
    })
      .then(res => {
        const file = res[0];
        const formData = new FormData();
        formData.append('file', {
          uri: file.uri,
          type: file.type,
          name: file.name,
        });

        setMainLoading(true);
        uploadRudiFileRequest(formData, (res: any) => {
          if (res) {
            setRudiFile(res);
            showToastMsg(TOAST_MESSAGES.FILE_UPLOADED, TOAST_TYPE.SUCCESS);
          }
          setMainLoading(false);
        });
      })
      .catch(error => {});
  };

  const renderInputToolbar = (props: any) => {
    return (
      <InputToolbar
        {...props}
        containerStyle={[
          styles.inputContainer,
          route?.params?.customPrompt
            ? [styles.inputContainer, {height: 400}]
            : {},
        ]}
        placeholderTextColor={Colors.text.placeHolderTextColor}
      />
    );
  };
  const onSend = (message: any, dailyNewsFlag: boolean) => {
    setMessages(previousMessages =>
      GiftedChat.append(previousMessages, message),
    );

    setText(undefined);

    setLoading(true);
    let messageObjectArray: any[] = [];

    if (rudiMessages?.messages?.length > 20) {
      rudiMessages.messages.slice(-20)?.forEach((message: Message) => {
        const newMessage = {
          content: message.content,
          role: message.senderType,
        };
        messageObjectArray.push(newMessage);
      });
    } else {
      rudiMessages.messages?.forEach((message: Message) => {
        const newMessage = {
          content: message.content,
          role: message.senderType,
        };
        messageObjectArray.push(newMessage);
      });
    }

    const newMessageToAppend = {
      content: message[0].text,
      role: 'user',
    };

    messageObjectArray.push(newMessageToAppend);

    const payload = {
      ageBucket: user.ageBucket,
      messageObject: messageObjectArray,
      summary: rudiMessages.summary,
      fileSummary: rudiFile && rudiFile.fileSummary ? rudiFile.fileSummary : '',
      convId: rudiMessages.convId,
      collectionName: rudiMessages.collectionName,
      count: messageObjectArray.length - 1,
      dailyNewsFlag: dailyNewsFlag,
      dailyNews: newsData,
    };
    sendRudiMessageRequest(payload, (res: any) => {
      if (res) {
        const newMessages = [
          {
            _id: Math.random().toString(),
            text: res.rudiMessage,
            createdAt: new Date(),
            news_articles: res.news_articles ?? [],
            user: {
              _id: RUDI_MESSAGE.BOT_ID,
              name: RUDI_MESSAGE.BOT_NAME,
              avatar: Image.resolveAssetSource(DefaultImage).uri,
            },
          },
        ];

        setMessages(previousMessages =>
          GiftedChat.append(previousMessages, newMessages),
        );

        setTimeout(() => {
          setLoading(false);
          setMainLoading(false);
        }, 1000);
      }
      setLoading(false);
      setMainLoading(false);
    });
  };
  const handleToggleAttachmentView = () => {
    setAttachmentView(true);
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        style={styles.navBar}
        titleImage={
          <View style={styles.titleWithImage}>
            <View style={styles.rudiChatContainer}>
              <RUDI_PROFILE_ICON height={25} width={25} />
            </View>
            <Text type={'semi_bold'} color={'black'} style={AppStyles.mLeft10}>
              {CHAT_LIST.RUDI}
            </Text>
          </View>
        }
        leftBtnPress={() => {
          Keyboard.dismiss();
          setTimeout(() => {
            navigation.goBack();
          }, 100);
        }}
        hasBack={true}
        hasRight
        rightBtnImage={<THREE_DOT_VERTICAL />}
        rightBtnPress={handleToggleAttachmentView}
      />
      <View style={styles.seperator}></View>
      {showAttachmentView && renderAttachmentView()}
      <GiftedChat
        keyboardShouldPersistTaps="handled"
        maxComposerHeight={Platform.OS === 'ios' ? 80 : 60}
        textInputProps={styles.textInputProps}
        renderTime={renderTime}
        listViewProps={{
          scrollEventThrottle: 600,
          onEndReachedThreshold: 0.1,
          onEndReached: () => {
            if (messages.length < rudiMessages?.totalMessageCount) {
              setPage(page + 1);
            }
          },
        }}
        showUserAvatar
        messages={messages}
        onSend={(messages: never[] | IMessage[]) =>
          onSend(messages, route?.params?.dailyNewsFlag ? true : false)
        }
        user={{
          _id: user.userId,
          name: user.displayName,
          avatar: user.avatarUrl,
        }}
        inverted={true}
        renderSend={(props: SendProps<IMessage>) => renderSend(props)}
        renderInputToolbar={(props: InputToolbarProps<IMessage>) =>
          renderInputToolbar(props)
        }
        showAvatarForEveryMessage={true}
        alwaysShowSend={!loading}
        renderUsernameOnMessage={true}
        renderBubble={(props: BubbleProps<IMessage>) => renderBubble(props)}
        renderAvatarOnTop
        bottomOffset={Platform.OS === 'ios' ? -20 : 0}
        renderFooter={() =>
          loading && (
            <View style={styles.footerComponent}>
              <TypingIndicator isTyping={true} />
              <Text style={styles.rudiText}>{RUDI_MESSAGE.RUDI_TYPING}</Text>
            </View>
          )
        }
      />
      <Loader loading={mainLoading} />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  rudiMessages: state.rudi.rudiMessage,
  user: state.user.userInfo,
});

export default connect(mapStateToProps, {
  getRudiMessagesRequest,
  getRudiFileRequest,
  sendRudiMessageRequest,
  uploadRudiFileRequest,
  deleteRudiFileRequest,
})(RudiChat);
