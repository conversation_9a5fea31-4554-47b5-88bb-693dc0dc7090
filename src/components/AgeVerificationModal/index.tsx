import React, {useState} from 'react';
import {View, Modal, Alert, ActivityIndicator} from 'react-native';
import {Text, AppButton, CustomNavbar} from '../index';
import {Colors, Fonts} from '../../theme';
import {styles} from './styles';
import {
  AGE_VERIFICATION,
  COMMON,
  PROFILE_SETUP,
} from '../../constants/StringConstants';
import YotiService from '../../services/YotiService';
import PublishButton from '../PublishButton';
import util from '../../util';
import {TouchableOpacity} from 'react-native-gesture-handler';

interface AgeVerificationModalProps {
  visible: boolean;
  onVerificationComplete: () => void;
  onStartVerification: (callback: (res: any) => void) => void;
  userInfoRequest: (callback: (res: any) => void) => void;
  logout: () => void;
}

const AgeVerificationModal: React.FC<AgeVerificationModalProps> = ({
  visible,
  onVerificationComplete,
  onStartVerification,
  userInfoRequest,
  logout,
}) => {
  const [loading, setLoading] = useState(false);
  const [verificationStarted, setVerificationStarted] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(false);

  const handleStartVerification = () => {
    setLoading(true);
    setVerificationStarted(false);

    onStartVerification((response: any) => {
      setLoading(false);

      if (response?.shareUrl) {
        setVerificationStarted(true);
        const yotiService = YotiService.getInstance();
        yotiService.openYotiApp(response.shareUrl, response.qrCode?.uri);
      } else {
        Alert.alert(
          AGE_VERIFICATION.ERROR_TITLE,
          AGE_VERIFICATION.SESSION_FAILED,
          [{text: AGE_VERIFICATION.RETRY, onPress: handleStartVerification}],
        );
      }
    });
  };

  const handleCheckVerification = () => {
    setCheckingStatus(true);

    userInfoRequest((userRes: any) => {
      setCheckingStatus(false);

      if (userRes?.ageVerificationError === true) {
        Alert.alert(
          AGE_VERIFICATION.SUCCESS_TITLE,
          AGE_VERIFICATION.SUCCESS_MESSAGE,
          [
            {
              text: AGE_VERIFICATION.CONTINUE,
              onPress: () => {
                setVerificationStarted(false);
                onVerificationComplete();
              },
            },
          ],
        );
      } else {
        Alert.alert(
          AGE_VERIFICATION.PENDING_TITLE,
          AGE_VERIFICATION.PENDING_MESSAGE,
          [
            {
              text: AGE_VERIFICATION.CHECK_AGAIN,
              onPress: handleCheckVerification,
            },
            {
              text: AGE_VERIFICATION.VERIFY_AGAIN,
              onPress: handleStartVerification,
            },
          ],
        );
      }
    });
  };

  const handleRetryVerification = () => {
    setVerificationStarted(false);
    handleStartVerification();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Prevent closing
    >
      <View style={styles.overlay}>
        {/* <TouchableOpacity
          onPress={logout}
          style={{
            top: 0,
            right: 0,
            position: 'absolute',
            backgroundColor: 'green',
          }}>
          <Text color="white">logout</Text>
        </TouchableOpacity> */}
        <CustomNavbar
          hasBack={false}
          style={{top: -190, backgroundColor: 'transparent'}}
          hasMultiRight={true}
          rightBtnImage={
            <PublishButton
              textColor="#000"
              size="xxSmall"
              buttonTitle={COMMON.LOGOUT}
              onPress={logout}
              isPublish={true}
            />
          }
        />
        <View style={styles.container}>
          <Text style={styles.title}>{AGE_VERIFICATION.TITLE}</Text>

          <Text style={styles.description}>
            {verificationStarted
              ? AGE_VERIFICATION.VERIFICATION_IN_PROGRESS
              : AGE_VERIFICATION.DESCRIPTION}
          </Text>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.black} />
              <Text style={styles.loadingText}>
                {AGE_VERIFICATION.STARTING_VERIFICATION}
              </Text>
            </View>
          ) : verificationStarted ? (
            <View style={styles.actionsContainer}>
              <Text style={styles.instructionText}>
                {AGE_VERIFICATION.COMPLETE_INSTRUCTION}
              </Text>

              <AppButton
                text={
                  checkingStatus
                    ? AGE_VERIFICATION.CHECKING_STATUS
                    : AGE_VERIFICATION.CHECK_VERIFICATION
                }
                onPress={handleCheckVerification}
                disabled={checkingStatus}
                buttonStye={styles.primaryButton}
                textColor={Colors.white}
                size={'xxSmall'}
              />

              <AppButton
                text={AGE_VERIFICATION.VERIFY_AGAIN}
                onPress={handleRetryVerification}
                buttonStye={styles.secondaryButton}
                textColor={Colors.black}
                size={'xxSmall'}
              />
            </View>
          ) : (
            <View style={styles.actionsContainer}>
              <AppButton
                text={AGE_VERIFICATION.START_VERIFICATION}
                onPress={handleStartVerification}
                buttonStye={styles.primaryButton}
                textColor={Colors.white}
              />
            </View>
          )}

          {verificationStarted && (
            <Text style={styles.noteText}>{AGE_VERIFICATION.NOTE_TEXT}</Text>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default AgeVerificationModal;
