import React, {useState} from 'react';
import {View, Modal, Alert, ActivityIndicator} from 'react-native';
import {Text, AppButton} from '../index';
import {Colors} from '../../theme';
import {styles} from './styles';
import {AGE_VERIFICATION, COMMON} from '../../constants/StringConstants';
import YotiService from '../../services/YotiService';
import {TouchableOpacity} from 'react-native-gesture-handler';
import DatePicker from 'react-native-date-picker';

interface AgeVerificationModalProps {
  visible: boolean;
  onVerificationComplete: () => void;
  onStartVerification: (callback: (res: any) => void) => void;
  userInfoRequest: (callback: (res: any) => void) => void;
  updateDobRequest: (payload: any, callback: (res: any) => void) => void;
  logout: () => void;
}

const AgeVerificationModal: React.FC<AgeVerificationModalProps> = ({
  visible,
  onVerificationComplete,
  onStartVerification,
  userInfoRequest,
  updateDobRequest,
  logout,
}) => {
  const [loading, setLoading] = useState(false);
  const [verificationStarted, setVerificationStarted] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [showDobCorrection, setShowDobCorrection] = useState(false);
  const [dob, setDob] = useState<Date>(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleStartVerification = () => {
    setLoading(true);
    setVerificationStarted(false);

    onStartVerification((response: any) => {
      setLoading(false);

      if (response?.shareUrl) {
        setVerificationStarted(true);
        const yotiService = YotiService.getInstance();
        yotiService.openYotiApp(response.qrCode.uri);
      } else {
        Alert.alert(
          AGE_VERIFICATION.ERROR_TITLE,
          AGE_VERIFICATION.SESSION_FAILED,
          [{text: AGE_VERIFICATION.RETRY, onPress: handleStartVerification}],
        );
      }
    });
  };

  const handleCheckVerification = () => {
    setCheckingStatus(true);

    userInfoRequest((userRes: any) => {
      setCheckingStatus(false);

      if (userRes?.ageVerified === true) {
        Alert.alert(
          AGE_VERIFICATION.SUCCESS_TITLE,
          AGE_VERIFICATION.SUCCESS_MESSAGE,
          [
            {
              text: AGE_VERIFICATION.CONTINUE,
              onPress: () => {
                setVerificationStarted(false);
                onVerificationComplete();
              },
            },
          ],
        );
      } else if (userRes?.ageVerificationError === true) {
        setShowDobCorrection(true);
        setVerificationStarted(false);
      } else {
        Alert.alert(
          AGE_VERIFICATION.PENDING_TITLE,
          AGE_VERIFICATION.PENDING_MESSAGE,
          [
            {
              text: AGE_VERIFICATION.CHECK_AGAIN,
              onPress: handleCheckVerification,
            },
            {
              text: AGE_VERIFICATION.VERIFY_AGAIN,
              onPress: handleStartVerification,
            },
            {
              text: COMMON.CANCEL,
              onPress: () => {},
            },
          ],
        );
      }
    });
  };

  const handleDobSubmission = () => {
    setLoading(true);
    const payload = {
      dob: dob.toISOString(),
    };

    updateDobRequest(payload, (res: any) => {
      setLoading(false);
      if (res) {
        // DOB correction successful
        setShowDobCorrection(false);
        Alert.alert(
          'DOB Updated Successfully',
          'Your date of birth has been updated. You can now continue using the app.',
          [
            {
              text: 'Continue',
              onPress: onVerificationComplete,
            },
          ],
        );
      } else {
        // DOB correction failed - show error message
        Alert.alert(
          'DOB Verification Failed',
          "The DOB you entered doesn't match the verified age from Yoti. Please correct your date of birth.",
          [{text: 'OK'}],
        );
      }
    });
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.title}>
            {showDobCorrection
              ? 'Correct Your Date of Birth'
              : AGE_VERIFICATION.TITLE}
          </Text>

          <Text style={styles.description}>
            {showDobCorrection
              ? "Your date of birth doesn't match the age verified by Yoti. Please enter the correct date of birth."
              : verificationStarted
              ? AGE_VERIFICATION.VERIFICATION_IN_PROGRESS
              : AGE_VERIFICATION.DESCRIPTION}
          </Text>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.black} />
              <Text style={styles.loadingText}>
                {AGE_VERIFICATION.STARTING_VERIFICATION}
              </Text>
            </View>
          ) : showDobCorrection ? (
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                style={styles.dobInputContainer}
                onPress={() => setShowDatePicker(true)}>
                <Text style={styles.dobInputText}>
                  {dob.toLocaleDateString()}
                </Text>
                <Text style={styles.dobInputLabel}>Tap to change date</Text>
              </TouchableOpacity>

              <DatePicker
                modal
                open={showDatePicker}
                date={dob}
                mode="date"
                maximumDate={new Date()}
                onConfirm={selectedDate => {
                  setShowDatePicker(false);
                  setDob(selectedDate);
                }}
                onCancel={() => {
                  setShowDatePicker(false);
                }}
              />

              <AppButton
                text="Update Date of Birth"
                onPress={handleDobSubmission}
                buttonStye={styles.primaryButton}
                textColor={Colors.white}
                size={'xxSmall'}
              />
            </View>
          ) : verificationStarted ? (
            <View style={styles.actionsContainer}>
              <Text style={styles.instructionText}>
                {AGE_VERIFICATION.COMPLETE_INSTRUCTION}
              </Text>

              <AppButton
                text={
                  checkingStatus
                    ? AGE_VERIFICATION.CHECKING_STATUS
                    : AGE_VERIFICATION.CHECK_VERIFICATION
                }
                onPress={handleCheckVerification}
                disabled={checkingStatus}
                buttonStye={styles.primaryButton}
                textColor={Colors.white}
                size={'xxSmall'}
              />

              <AppButton
                text={COMMON.LOGOUT}
                onPress={logout}
                buttonStye={styles.secondaryButton}
                textColor={Colors.black}
                size={'xxSmall'}
              />
            </View>
          ) : (
            <View style={styles.actionsContainer}>
              <AppButton
                text={AGE_VERIFICATION.START_VERIFICATION}
                onPress={handleStartVerification}
                buttonStye={styles.primaryButton}
                textColor={Colors.white}
              />
              <AppButton
                text={COMMON.LOGOUT}
                onPress={logout}
                buttonStye={styles.secondaryButton}
                textColor={Colors.black}
                size={'xxSmall'}
              />
            </View>
          )}

          {verificationStarted && (
            <Text style={styles.noteText}>{AGE_VERIFICATION.NOTE_TEXT}</Text>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default AgeVerificationModal;
