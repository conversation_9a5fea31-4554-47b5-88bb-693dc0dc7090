import {FlatList, Text, View} from 'react-native';
import styles from './styles';
import {SubscriptionFeatures, ISubscriptionPackage} from '../../types';
import {GREEN_CHECK, RED_UNCHECK} from '../../constants/AssetSVGConstants';
import AppButton from '../AppButton';

interface ISubscriptionPackageProps {
  features: SubscriptionFeatures[];
  subscriptionPackage: ISubscriptionPackage;
  onPress: (name: string) => void;
}

const SubscriptionPackage: React.FC<ISubscriptionPackageProps> = ({
  features,
  subscriptionPackage,
  onPress,
}) => {
  const getColor = (name: string) => {
    if (name === 'Free') {
      return '#101418';
    } else if (name === 'Gold') {
      return '#C1900E';
    } else if (name === 'Premium') {
      return '#779097';
    } else if (name === 'Silver') {
      return '#BDBEC0';
    }
  };

  const isAvailable = (featureName: string) => {
    const feature = features.find(
      feature => feature.featureName === featureName,
    );
    if (feature) {
      const tier = feature.accessTier.find(
        tier => tier.tierName === subscriptionPackage.name,
      );
      if (tier) {
        return tier.accessAllowed;
      }
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View
        style={[
          styles.headerContainer,
          {backgroundColor: getColor(subscriptionPackage.name)},
        ]}>
        <Text style={styles.title}>{subscriptionPackage.name} Tier</Text>
        <Text
          style={[
            styles.subTitle,
            {color: subscriptionPackage.name === 'Free' ? 'white' : 'black'},
          ]}>
          £{subscriptionPackage.amount} | Per Month
        </Text>
      </View>
      <View style={styles.contentContainer}>
        <FlatList
          data={features}
          keyExtractor={item => item.id.toString()}
          renderItem={({item}) => (
            <View
              style={{
                flexDirection: 'row',
                paddingVertical: 5,
                paddingHorizontal: 15,
                alignItems: 'center',
              }}>
              {isAvailable(item.featureName) ? (
                <GREEN_CHECK />
              ) : (
                <RED_UNCHECK />
              )}
              <Text style={styles.featureText}>{item.featureName}</Text>
            </View>
          )}
        />
        <AppButton
          text={`GET ${subscriptionPackage.name.toUpperCase()} PACKAGE`}
          buttonStye={{marginVertical: 20}}
          textColor={'#fff'}
          onPress={() => onPress(subscriptionPackage.name)}
        />
      </View>
    </View>
  );
};

export default SubscriptionPackage;
