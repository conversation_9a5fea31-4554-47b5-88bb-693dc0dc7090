import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';

export default StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    marginVertical: 20,
    marginLeft: 18,
    marginRight: 10,
  },
  headerContainer: {
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    height: Metrics.ratio(95),
  },
  title: {
    fontFamily: Fonts.type.semi_bold,
    fontSize: 24,
    color: Colors.white,
    paddingTop: 20,
    paddingLeft: 24,
  },
  subTitle: {
    fontFamily: Fonts.type.regular,
    fontSize: 14,
    paddingTop: 2,
    paddingLeft: 24,
  },
  contentContainer: {
    flex: 1,
    paddingTop: 10,
    backgroundColor: '#F3F6F6',
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: 20,
  },
  featureText: {
    fontFamily: Fonts.type.regular,
    fontSize: 12,
    color: Colors.black,
    paddingLeft: 12,
    paddingRight: 45,
  },
});
