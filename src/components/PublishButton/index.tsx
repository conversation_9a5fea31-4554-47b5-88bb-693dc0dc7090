import React from 'react';
import {
  TouchableOpacity,
  TouchableNativeFeedback,
  View,
  TouchableOpacityProps,
  ViewStyle,
} from 'react-native';
import Util from '../../util';
import {ButtonView, Text} from '..';
import Metrics from '../../theme/Metrics';
import {Colors} from '../../theme';
import {CREATE_IMPRINT} from '../../constants/StringConstants';
import styles from './styles';

interface publishButton {
  isPublish: boolean;
  onPress: () => void;
  buttonTitle?: string;
  size?:
    | 'small'
    | 'buttonText'
    | 'xxxxSmall'
    | 'xxxSmall'
    | 'xxSmall'
    | 'xSmall'
    | 'normal'
    | 'medium'
    | 'Large'
    | 'large'
    | 'xLarge'
    | 'xxLarge'
    | 'xxxLarge'
    | 'xxxxLarge';
}

const PublishButton: React.FC<publishButton> = ({
  isPublish = false,
  onPress,
  buttonTitle,
  size,
}) => {
  return (
    <ButtonView
      disabled={!isPublish}
      onPress={onPress}
      style={[styles.button, isPublish ? styles.selectedColor : {}]}>
      <Text
        color={isPublish ? Colors.white : Colors.text.publishTextInActiveColor}
        size={size ?? 'buttonText'}>
        {buttonTitle ? buttonTitle : CREATE_IMPRINT.PUBLISH}
      </Text>
    </ButtonView>
  );
};

export default PublishButton;
