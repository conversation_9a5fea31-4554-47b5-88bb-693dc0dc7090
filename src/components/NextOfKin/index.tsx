import {Text, View, Pressable, ActivityIndicator, Alert} from 'react-native';
import {Colors} from '../../theme';
import {
  CROSS_ICON,
  TICK_BLUE,
  TICK_ICON,
  TICK_ICON_NEXT,
} from '../../constants/AssetSVGConstants';
import styles from './styles';
import AppButton from '../AppButton';
import NextOfKindDetails from '../NextOfKinDetails';
import {FlatList} from 'react-native-gesture-handler';
import {
  nextOfKinRequest,
  deleteNextOfKinRequest,
  addNextOfKinRequest,
  remindNextOfKinRequest,
} from '../../actions/TimelineActions';
import {connect} from 'react-redux';
import {useEffect, useState} from 'react';
import {INextOfKin} from '../../types';
import {showToastMsg} from '../Alert';
import {NEXT_OF_KIN, TOAST_TYPE} from '../../constants/StringConstants';
import _ from 'lodash';
import util from '../../util';

interface NextOfKinProps {
  nextOfKinRequest: (callback: (res: any) => void) => void;
  remindNextOfKinRequest: (payload: any, callback: (res: any) => void) => void;
  deleteNextOfKinRequest: (payload: any, callback: (res: any) => void) => void;
  addNextOfKinRequest: (payload: any, callback: (res: any) => void) => void;
  crossClicked: () => void;
  ownEmail?: string;
}

const NextOfKin: React.FC<NextOfKinProps> = ({
  nextOfKinRequest,
  deleteNextOfKinRequest,
  crossClicked,
  addNextOfKinRequest,
  remindNextOfKinRequest,
  ownEmail,
}) => {
  const [nextOfKin, setNextOfKin] = useState<INextOfKin[]>([]);
  const [checkClicked, setCheckClicked] = useState(false);
  const [safetyCheck, setSafetyChecked] = useState(false);

  const [buttonText, setButtonText] = useState(NEXT_OF_KIN.SUBMIT);
  const [isLoading, setLoading] = useState(false);

  const handleUpdate = (index: number, updatedData: INextOfKin) => {
    const updatedNextOfKin = [...nextOfKin];
    updatedNextOfKin[index] = updatedData;
    setNextOfKin(updatedNextOfKin);
  };

  useEffect(() => {
    nextOfKinRequest((res: INextOfKin[]) => {
      let data = res;
      if (_.isEmpty(res)) {
        data.push({
          name: '',
          email: '',
          relation: '',
          isEditAble: true,
        });
        data.push({
          name: '',
          email: '',
          relation: '',
          isEditAble: true,
        });
      }
      if (res.length < 2) {
        data.push({
          name: '',
          email: '',
          relation: '',
          isEditAble: true,
        });
      }

      setNextOfKin(data);
    });
  }, []);

  const handleAddNew = () => {
    setNextOfKin([
      ...nextOfKin,
      {
        name: '',
        email: '',
        relation: '',
        isEditAble: true,
      },
    ]);
  };

  useEffect(() => {
    if (nextOfKin && nextOfKin.length) {
      const hasReminder = nextOfKin.every(item => 'id' in item);
      if (hasReminder) {
        setButtonText(NEXT_OF_KIN.REMIND);
      } else {
        setButtonText(NEXT_OF_KIN.SUBMIT);
      }
    }
  }, [nextOfKin]);

  const handleSubmitClick = () => {
    if (!checkClicked) {
      return;
    }
    if (!safetyCheck) {
      return;
    }

    for (let i = 0; i < nextOfKin.length; i++) {
      const kin = nextOfKin[i];

      if (!kin.name) {
        showToastMsg(`${NEXT_OF_KIN.PLEASE_FILL_NAME}${i + 1} `);
        return;
      }
      if (!kin.email) {
        showToastMsg(`${NEXT_OF_KIN.PLEASE_FILL_EMAIL}${i + 1} `);
        return;
      }
      if (!util.isEmailValid(kin.email)) {
        showToastMsg(`${NEXT_OF_KIN.PLEASE_VALID_EMAIL}${i + 1} `);
        return;
      }
      if (kin.email === ownEmail) {
        showToastMsg(`${NEXT_OF_KIN.EMAIL_SHOULD_NOT_OWN}${i + 1} `);
        return;
      }
      if (!kin.relation) {
        showToastMsg(`${NEXT_OF_KIN.PLEASE_FILL_RELATION}${i + 1} `);
        return;
      }
    }
    const hasReminder = nextOfKin.every(item => 'id' in item);

    const hasAnyPending = nextOfKin.some(
      (kin: INextOfKin) => kin.hasApproved === null,
    );

    if (!hasAnyPending && hasReminder) {
      Alert.alert(NEXT_OF_KIN.ADD_NEW_NEXT_OF_KIN_BEFORE_REMINDER);
      return;
    }

    let payload: INextOfKin[] = nextOfKin.filter(
      kin => kin.name && kin.email && kin.relation && !kin.id,
    );

    if (_.isEmpty(payload) && nextOfKin.length < 2) {
      showToastMsg(NEXT_OF_KIN.ATLEAST_TWO_KIN);
      return;
    } else {
      setLoading(true);
      const hasReminder = nextOfKin.every(item => 'id' in item);
      const newPayload = payload.map(obj => {
        const {isEditAble, ...rest} = obj;
        return rest;
      });
      if (!hasReminder) {
        addNextOfKinRequest(newPayload, (res: any) => {
          if (res) {
            setLoading(false);

            showToastMsg(NEXT_OF_KIN.NEXT_OF_KIN_ADDED, TOAST_TYPE.SUCCESS);
            crossClicked();
          }
          setLoading(false);
        });
      } else {
        setLoading(true);
        remindNextOfKinRequest(payload, res => {
          crossClicked();
          showToastMsg(NEXT_OF_KIN.REMIND_NEXT_OF_KIN, TOAST_TYPE.SUCCESS);
          setLoading(false);
        });
      }
    }
  };

  const onDelete = (index: number, data: INextOfKin) => {
    if (nextOfKin.length === 2) {
      showToastMsg(NEXT_OF_KIN.ATLEAST_TWO_KIN);
      return;
    } else {
      if (data.id) {
        deleteNextOfKinRequest({id: data.id}, (res: any) => {
          const updatedNextOfKin = nextOfKin.filter((_, i) => i !== index);
          setNextOfKin(updatedNextOfKin);
        });
      } else {
        const updatedNextOfKin = nextOfKin.filter((_, i) => i !== index);
        setNextOfKin(updatedNextOfKin);
      }
    }
  };

  const onRemind = (data: INextOfKin) => {
    setLoading(true);
    const payload = {
      id: data.id,
    };

    remindNextOfKinRequest(payload, res => {
      if (res) {
        setLoading(false);
        showToastMsg(NEXT_OF_KIN.REMIND_NEXT_OF_KIN, TOAST_TYPE.SUCCESS);
      }
    });
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{NEXT_OF_KIN.NEXT_OF_VERIFICATION}</Text>
          <Pressable style={styles.cross} onPress={() => crossClicked()}>
            <CROSS_ICON width={12} height={12} />
          </Pressable>
        </View>
        <View></View>
        <FlatList
          data={nextOfKin}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item, index}) => (
            <NextOfKindDetails
              data={item}
              onUpdate={updatedData => handleUpdate(index, updatedData)}
              onDelete={() => onDelete(index, item)}
              onRemind={() => onRemind(item)}
            />
          )}
          extraData={nextOfKin}
          ListHeaderComponent={() => (
            <Text style={styles.kinHeading}>
              {NEXT_OF_KIN.DETAILS} {'\n'}
              <Text style={styles.kinDescription}>
                {NEXT_OF_KIN.DESCRIPTION}
              </Text>
            </Text>
          )}
          ListFooterComponent={() => (
            <>
              <View style={styles.above18Container}>
                <Pressable onPress={() => setCheckClicked(!checkClicked)}>
                  {checkClicked ? (
                    <TICK_BLUE width={25} height={25} />
                  ) : (
                    <TICK_ICON_NEXT width={25} height={25} />
                  )}
                </Pressable>
                <Text style={styles.above18Text}>{NEXT_OF_KIN.ABOVE_18}</Text>
              </View>
              <View style={styles.safetyContainer}>
                <Pressable onPress={() => setSafetyChecked(!safetyCheck)}>
                  {safetyCheck ? (
                    <TICK_BLUE width={25} height={25} />
                  ) : (
                    <TICK_ICON_NEXT width={25} height={25} />
                  )}
                </Pressable>
                <Text style={styles.above18Text}>
                  {NEXT_OF_KIN.SAFETY_CHECK}
                </Text>
              </View>
            </>
          )}
        />

        <View style={styles.buttonContainer}>
          <AppButton
            buttonStye={styles.retakeButton}
            text={NEXT_OF_KIN.ADD_NEW}
            onPress={() => handleAddNew()}
          />
          <AppButton
            buttonStye={[
              styles.nextButton,
              {
                backgroundColor:
                  !checkClicked || !safetyCheck
                    ? Colors.lightGray
                    : Colors.BlackButton,
              },
            ]}
            text={buttonText}
            textColor={Colors.white}
            onPress={() => handleSubmitClick()}
          />
        </View>
      </View>
      {isLoading && <ActivityIndicator style={styles.loader} size={'large'} />}
    </View>
  );
};

const mapStateToProps = (state: any) => ({user: state.user});

export default connect(mapStateToProps, {
  nextOfKinRequest,
  deleteNextOfKinRequest,
  addNextOfKinRequest,
  remindNextOfKinRequest,
})(NextOfKin);
