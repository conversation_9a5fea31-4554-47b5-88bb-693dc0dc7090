import _ from 'lodash';
import React, {useRef, useState} from 'react';
import {AppButton, TextInput, Loader} from '../../';
import {View, TextInput as RNTextInput, Pressable} from 'react-native';
import styles from './styles';
import {DATE_ICON, PROFILE_ICON} from '../../../constants/AssetSVGConstants';
import {Colors, Metrics} from '../../../theme';
import {PROFILE_SETUP, SIGN_UP} from '../../../constants/StringConstants';
import {UserInfo} from '../../../types';
import DatePicker from 'react-native-date-picker';
import util from '../../../util';

interface ActionsProps {
  personalInformation: UserInfo;
  nextButton: () => void;
  onBlur?: () => any;
  setUserName: (val: string) => void;
  setDisplayName: (val: string) => void;
  correct?: boolean;
  userName: string;
  onFocus?: () => any;
  displayName: string;
  dob: Date | undefined;
  setDob: (Date: Date) => void;
  showValue: boolean;
}

export const PersonalInformation: React.FC<ActionsProps> = ({
  nextButton,
  setUserName,
  setDisplayName,
  correct,
  userName,
  onFocus,
  displayName,
  onBlur,
  personalInformation,
  dob,
  setDob,
  showValue,
}) => {
  const userNameRef = useRef<RNTextInput>(null);
  const displayNameREf = useRef<RNTextInput>(null);
  const [open, setOpen] = useState(false);

  console.log('PersonalInformation dob:', [personalInformation]);

  return (
    <View style={styles.container}>
      <TextInput
        ref={userNameRef}
        containerStyle={styles.emailInputContainer}
        label={PROFILE_SETUP.USER_NAME}
        placeholder={PROFILE_SETUP.PLACEHOLDER}
        leftImage={<PROFILE_ICON />}
        onChangeText={val => setUserName(val)}
        value={userName}
        isCorrect={correct || false}
        onBlur={onBlur ? onBlur : () => {}}
        onFocus={onFocus ? onFocus : () => {}}
        onSubmitEditing={() => displayNameREf.current?.focus()}
      />
      <TextInput
        ref={displayNameREf}
        containerStyle={styles.passwordInputContainer}
        label={PROFILE_SETUP.DISPLAY_NAME}
        placeholder={PROFILE_SETUP.PLACEHOLDER}
        leftImage={<PROFILE_ICON />}
        value={displayName}
        onChangeText={val => setDisplayName(val)}
      />
      {!personalInformation?.profile?.dob && (
        <>
          <Pressable
            style={{
              marginHorizontal: Metrics.ratio(30),
              marginVertical: Metrics.ratio(10),
            }}
            onPress={() => setOpen(!open)}>
            <TextInput
              label={SIGN_UP.DOB_LABEL}
              placeholder={SIGN_UP.DOB_PLACEHOLDER}
              rightImage={<DATE_ICON />}
              value={dob ? util.getFormattedDate(dob) : ''}
              onPress={() => {
                setOpen(!open);
              }}
              editable={false}
              onPressIn={() => setOpen(!open)}
            />
          </Pressable>

          <DatePicker
            // maximumDate={util.getMinimumDate()}
            modal
            locale="en"
            mode="date"
            open={open}
            date={dob ? dob : new Date()}
            onConfirm={date => {
              setOpen(false);
              setDob(date);
            }}
            onCancel={() => {
              setOpen(false);
            }}
          />
        </>
      )}
      <AppButton
        buttonStye={{marginTop: Metrics.doubleBaseMargin}}
        text={PROFILE_SETUP.NEXT}
        onPress={nextButton}
        textColor={Colors.text.white}
      />
    </View>
  );
};
