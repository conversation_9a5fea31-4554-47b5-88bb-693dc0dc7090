// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles} from '../../../theme';
export default StyleSheet.create({
  container: {
    paddingTop: 30,
  },
  button: {
    marginTop: Metrics.ratio(30),
  },
  headerText: {
    color: '#000',
    marginTop: Metrics.ratio(40),
    marginBottom: Metrics.ratio(30),
    marginLeft: Metrics.ratio(24),
  },
  emailInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: Metrics.ratio(20),
  },
  passwordInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: Metrics.ratio(10),
  },
  phoneContainer: {
    paddingTop: Metrics.ratio(10),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Metrics.ratio(24),
  },
  phoneTextInput: {
    padding: 0,
  },
  phoneInputContainer: {
    height: Metrics.ratio(60),
    paddingVertical: 0,
    backgroundColor: Colors.white,
    borderRadius: Metrics.ratio(16),
    paddingRight: Metrics.ratio(15),
    borderColor: '#DAE0E6',
    borderWidth: Metrics.ratio(1),
  },
  errorText: {
    color: 'red',
    fontSize: Metrics.ratio(12),
    marginTop: Metrics.ratio(5),
  },
});
