// @flow
import {StyleSheet} from 'react-native';
import {Colors, Metrics, AppStyles} from '../../../theme';
export default StyleSheet.create({
  container: {
    paddingTop: 30,
  },
  button: {
    marginTop: Metrics.ratio(30),
  },
  headerText: {
    color: '#000',
    marginTop: Metrics.ratio(40),
    marginBottom: Metrics.ratio(30),
    marginLeft: Metrics.ratio(24),
  },
  emailInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: Metrics.ratio(20),
  },
  passwordInputContainer: {
    marginHorizontal: Metrics.ratio(30),
    marginVertical: Metrics.ratio(10),
  },
});
