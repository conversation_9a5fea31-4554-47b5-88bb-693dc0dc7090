import React, {useState} from 'react';
import {KeyboardAvoidingView, Platform} from 'react-native';
import styles from './styles'; // Import styles if required
import WebView from 'react-native-webview';
import {CustomNavbar, Loader} from '..';
import {useNavigation} from '@react-navigation/native';
import PublishButton from '../PublishButton';
import {CHAT_LIST} from '../../constants/StringConstants';

interface ReportScreenProps {
  route?: {
    params: {
      url: string;
      title: string;
    };
  };
}

const ReportScreen: React.FC<ReportScreenProps> = ({route}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const injectedJavaScript = `
    document.addEventListener("DOMContentLoaded", function() {
      var videos = document.querySelectorAll("video");
      videos.forEach(video => {
        video.setAttribute("playsinline", "");
        video.setAttribute("autoplay", "true");
        video.play();
      });
    });
    true;  // note: this is required to return after injecting JS
  `;
  const navigation = useNavigation();
  console.log('ReportScreen route params:', route?.params.url);

  const handleNavigationStateChange = (navState: {
    url: any;
    navigationType: any;
  }) => {
    const {url, navigationType} = navState;

    // Check if payment is successful
    if (url.includes('success=true')) {
      navigation.goBack();
    }
    // Check if redirected to the login page with a callbackUrl
    else if (navigationType === 'click' && url.includes('login?callbackUrl=')) {
      // Prevent navigation to this URL and go back in app instead
      navigation.goBack();
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior="padding"
      enabled={Platform.OS === 'android'}>
      <CustomNavbar
        title={route && route.params.title}
        leftBtnPress={() => navigation.goBack()}
        hasRight
        hasMultiRight
        rightBtnImage1={
          route &&
          route.params.title === CHAT_LIST.REPORT && (
            <PublishButton
              buttonTitle={'Done'}
              isPublish={true}
              onPress={() => {
                navigation.goBack();
              }}
            />
          )
        }
      />

      <WebView
        cacheMode="LOAD_NO_CACHE"
        source={{uri: route.params.url}}
        injectedJavaScript={injectedJavaScript}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        originWhitelist={['*']}
        useWebKit={true}
        allowsAirPlayForMediaPlayback={true}
        onLoad={() => setLoading(false)}
        onNavigationStateChange={handleNavigationStateChange}
      />

      <Loader loading={loading} />
    </KeyboardAvoidingView>
  );
};

export default ReportScreen;
