import {StyleSheet} from 'react-native';
import {Metrics, Colors} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(24),
    marginVertical: Metrics.ratio(12),
    padding: Metrics.ratio(12),
    borderRadius: Metrics.ratio(12),
  },
  header: {
    overflow: 'visible',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    width: Metrics.screenWidth * 0.75,
  },

  button: {
    borderRadius: Metrics.ratio(6),
    backgroundColor: Colors.gray2,
    paddingVertical: Metrics.ratio(4),
    paddingHorizontal: Metrics.ratio(12),
  },
  buttonText: {
    marginHorizontal: Metrics.ratio(10),
    marginVertical: Metrics.ratio(4),
  },
  imagePadding: {},
  bottomContainer: {flexDirection: 'row', alignSelf: 'flex-end'},
  buttonLeft: {marginLeft: Metrics.ratio(20)},
  shareIcon: {
    padding: Metrics.ratio(5),
    borderRadius: Metrics.ratio(2),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.gray,
  },
  symbol: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(5),
    marginRight: Metrics.ratio(10),
    borderRadius: Metrics.ratio(16),
    backgroundColor: '#EBEBEB',
  },

  logoText: {
    paddingLeft: Metrics.ratio(10),
    marginRight: Metrics.ratio(10),
  },
  itemContainer: {
    backgroundColor: '#F1F1F2',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: Metrics.ratio(100),
    paddingHorizontal: Metrics.ratio(10),
    paddingVertical: Metrics.ratio(2),
  },
  subContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Metrics.ratio(10),
  },
});
