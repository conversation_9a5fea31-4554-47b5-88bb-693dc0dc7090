import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    paddingTop: Metrics.ratio(10),
    alignItems: 'center',
    justifyContent: 'space-around',
    flexDirection: 'row',
    backgroundColor: Colors.white,
  },
  tabChild: {
    justifyContent: 'center',
    alignSelf: 'center',
    height: Metrics.ratio(52),
    alignItems: 'center',
    flex: 1,
  },
  selectedTab: {
    backgroundColor: Colors.gray,
    paddingHorizontal: Metrics.ratio(24),
    paddingVertical: Metrics.ratio(10),
    borderRadius: Metrics.ratio(10),
    borderWidth: Metrics.ratio(1),
    borderColor: 'transparent',
    overflow: 'hidden',
    marginBottom: Metrics.ratio(10),
  },
  whiteColor: {backgroundColor: Colors.white, marginBottom: Metrics.ratio(10)},
});
