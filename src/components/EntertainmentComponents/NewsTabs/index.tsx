import {View} from 'react-native';
import {styles} from './styles';
import {Text} from '../../';
import {ButtonView} from '../../';
import {Colors} from '../../../theme';
import {NEWS} from '../../../constants/StringConstants';

interface NewsTabProps {
  onGeneralPress: () => void;
  onPersonalPress: () => void;
  isPersonalSelect: boolean;
}

const NewsTab: React.FC<NewsTabProps> = ({
  onGeneralPress,
  onPersonalPress,
  isPersonalSelect,
}) => {
  return (
    <View style={styles.container}>
      <ButtonView onPress={onPersonalPress} style={styles.tabChild}>
        <Text
          color={
            isPersonalSelect
              ? Colors.tabsTextColor.activeTabColor
              : Colors.tabsTextColor.inActiveTabColor
          }
          size={'normal'}
          type={isPersonalSelect ? 'semi_bold' : 'regular'}
          style={isPersonalSelect ? styles.selectedTab : styles.whiteColor}>
          {NEWS.MY_NEWS}
        </Text>
      </ButtonView>
      <ButtonView onPress={onGeneralPress} style={styles.tabChild}>
        <Text
          color={
            !isPersonalSelect
              ? Colors.tabsTextColor.activeTabColor
              : Colors.tabsTextColor.inActiveTabColor
          }
          size={'normal'}
          type={!isPersonalSelect ? 'semi_bold' : 'regular'}
          style={!isPersonalSelect ? styles.selectedTab : styles.whiteColor}>
          {NEWS.GENERAL_NEWS}
        </Text>
      </ButtonView>
    </View>
  );
};

export default NewsTab;
