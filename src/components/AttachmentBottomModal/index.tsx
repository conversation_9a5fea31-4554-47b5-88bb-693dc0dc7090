import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Keyboard, Platform, View} from 'react-native';
import {BottomSheetModal, BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {AppStyles, Colors, Metrics} from '../../theme';
import {ButtonView, Text} from '..';
import {
  CAMERA_GREEN,
  ATTACHMENT_ICON,
  LOCATION_MARKER,
  TICK_BLUE,
  TAG_PEOPLE,
  MICROPHONE_PURPLE,
} from '../../constants/AssetSVGConstants';
import {CREATE_IMPRINT} from '../../constants/StringConstants';
import {styles} from './styles';

interface BottomSheetModalProps {
  onMediaPress: () => void;
  onTagPress: () => void;
  onArticlesPress: () => void;
  onCheckInPress: () => void;
  onVerifyPress: () => void;
  bottomSheetModalRef: any;
  verify: boolean;
  onRecordPress: () => void;
}

const AttachmentBottomModal: React.FC<BottomSheetModalProps> = ({
  onMediaPress,
  onArticlesPress,
  onCheckInPress,
  onVerifyPress,
  bottomSheetModalRef,
  verify,
  onTagPress,
  onRecordPress,
}) => {
  const snapPoints = useMemo(
    () => ['10%', Platform.OS == 'android' ? '55%' : '50%'],
    [],
  );

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        handleIndicatorStyle={styles.handleStyle}
        contentHeight={Metrics.screenHeight * 0.5}
        backgroundStyle={styles.bottomSheet}
        enablePanDownToClose={false}
        ref={bottomSheetModalRef}
        android_keyboardInputMode="adjustPan"
        index={1}
        snapPoints={snapPoints}>
        <ButtonView onPress={onMediaPress} style={styles.mediaButton}>
          <CAMERA_GREEN />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.MEDIA}
          </Text>
        </ButtonView>
        <ButtonView
          onPress={onRecordPress}
          style={[
            styles.mediaButton,
            {marginTop: Metrics.ratio(20), paddingLeft: 10},
          ]}>
          <MICROPHONE_PURPLE />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft10}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.SPEAK}
          </Text>
        </ButtonView>
        <ButtonView onPress={onTagPress} style={styles.tagButton}>
          <TAG_PEOPLE />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.TAG_PEOPLE}
          </Text>
        </ButtonView>
        <ButtonView onPress={onArticlesPress} style={styles.commonButtonStyle}>
          <ATTACHMENT_ICON />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.ARTICLE}
          </Text>
        </ButtonView>
        <ButtonView onPress={onCheckInPress} style={styles.checkInButton}>
          <LOCATION_MARKER />
          <Text
            color={Colors.itemColors.subTitleColor}
            style={AppStyles.mLeft5}
            size={'medium'}
            type="medium">
            {CREATE_IMPRINT.CHECK_IN}
          </Text>
        </ButtonView>
        <ButtonView onPress={onVerifyPress} style={styles.verifyButtonStyle}>
          <View style={AppStyles.flexRow}>
            <TICK_BLUE />
            <Text
              color={Colors.itemColors.subTitleColor}
              style={AppStyles.mLeft5}
              size={'medium'}
              type="medium">
              {CREATE_IMPRINT.VERIFY}
            </Text>
          </View>
          <View
            style={[
              styles.unSelectToggle,
              !verify ? styles.selectedToggle : {},
            ]}
          />
        </ButtonView>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

export default AttachmentBottomModal;
