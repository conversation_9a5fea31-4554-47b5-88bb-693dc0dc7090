import React, {useState} from 'react';
import {Alert, Modal, TextInput, Pressable, View, Text} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import styles from './styles';
import {
  CROSS_GRAY,
  CROSS_ICON,
  CROSS_ROUND,
} from '../../constants/AssetSVGConstants';

interface NoInviteDisclaimerProps {
  visible: boolean;
  onDone: () => void;
  onClose: () => void;
}

const NoInviteDisclaimerAlert: React.FC<NoInviteDisclaimerProps> = ({
  visible,
  onDone,
  onClose,
}) => {
  const handleDone = () => {
    onDone();
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}>
      <View style={{flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.7)'}}>
        <View style={styles.popup}>
          <View style={styles.popupInnerContainer}>
            <Text style={styles.placeHolder}>{'No Invite Code'}</Text>

            <Pressable
              style={{
                alignSelf: 'flex-end',
                flex: 1,
                top: -25,
                right: -10,
                alignItems: 'flex-end',
              }}
              onPress={onClose}>
              <CROSS_GRAY />
            </Pressable>
            <Text style={styles.title}>
              {
                'Thank you for joining Imprint.live, you are a valued member of our ever-growing waitlist and we look forward to welcoming you on the platform soon. Please look out in your emails for an exclusive invite code when we open to our next tranche of users.'
              }
            </Text>
            <Pressable onPress={handleDone} style={styles.doneButton}>
              <Text style={{color: 'white', fontFamily: Fonts.type.medium}}>
                Ok
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default NoInviteDisclaimerAlert;
