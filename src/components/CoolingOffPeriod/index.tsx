import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  StyleSheet,
} from 'react-native';
import {connect} from 'react-redux';
import {
  cancelCoolingOffRequest,
  checkCoolingOffStatusRequest,
} from '../../actions/UserActions';
import {SUBSCRIPTION} from '../../constants/StringConstants';
import {Colors} from '../../constants/Colors';

interface CoolingOffPeriodProps {
  user: any;
  cancelCoolingOffRequest: (payload: any, callback: any) => void;
  checkCoolingOffStatusRequest: (payload: any, callback: any) => void;
  onCancelSuccess?: () => void;
}

const CoolingOffPeriod: React.FC<CoolingOffPeriodProps> = ({
  user,
  cancelCoolingOffRequest,
  checkCoolingOffStatusRequest,
  onCancelSuccess,
}) => {
  const [coolingOffStatus, setCoolingOffStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkCoolingOffStatus();
  }, []);

  const checkCoolingOffStatus = () => {
    const payload = {
      userId: user?.userInfo?.userId,
    };

    checkCoolingOffStatusRequest(payload, (response: any, error: any) => {
      if (response && response.success) {
        setCoolingOffStatus(response.data);
      } else {
        console.error('Failed to check cooling-off status:', error);
      }
    });
  };

  const handleCancelSubscription = () => {
    Alert.alert(
      SUBSCRIPTION.COOLING_OFF_CANCEL_TITLE,
      SUBSCRIPTION.COOLING_OFF_CANCEL_MESSAGE,
      [
        {
          text: 'Keep Subscription',
          style: 'cancel',
        },
        {
          text: 'Cancel Subscription',
          style: 'destructive',
          onPress: () => cancelSubscription(),
        },
      ],
    );
  };

  const cancelSubscription = () => {
    setLoading(true);
    const payload = {
      userId: user?.userInfo?.userId,
      subscriptionId: coolingOffStatus?.subscriptionId,
    };

    cancelCoolingOffRequest(payload, (response: any, error: any) => {
      setLoading(false);
      if (response && response.success) {
        Alert.alert(
          'Success',
          SUBSCRIPTION.COOLING_OFF_CANCEL_SUCCESS,
          [
            {
              text: 'OK',
              onPress: () => {
                if (onCancelSuccess) {
                  onCancelSuccess();
                }
              },
            },
          ],
        );
      } else {
        Alert.alert(
          'Error',
          error?.message || 'Failed to cancel subscription',
        );
      }
    });
  };

  const calculateRemainingDays = () => {
    if (!coolingOffStatus?.coolingOffEndDate) return 0;
    
    const endDate = new Date(coolingOffStatus.coolingOffEndDate);
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  };

  if (!coolingOffStatus || coolingOffStatus.status !== 'cooling_off') {
    return null;
  }

  const remainingDays = calculateRemainingDays();

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.title}>
          {SUBSCRIPTION.COOLING_OFF_ACTIVE_TITLE}
        </Text>
        
        <Text style={styles.message}>
          {SUBSCRIPTION.COOLING_OFF_ACTIVE_MESSAGE.replace(
            '{days}',
            remainingDays.toString(),
          )}
        </Text>

        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            • You will not be charged until the cooling-off period ends
          </Text>
          <Text style={styles.infoText}>
            • You can cancel anytime during this period without charge
          </Text>
          <Text style={styles.infoText}>
            • After {remainingDays} days, your subscription will be activated
          </Text>
        </View>

        <TouchableOpacity
          style={styles.cancelButton}
          onPress={handleCancelSubscription}
          disabled={loading}>
          <Text style={styles.cancelButtonText}>
            {loading ? 'Cancelling...' : 'Cancel Subscription'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  card: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: Colors.ORANGE,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.BLACK,
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: Colors.GRAY_DARK,
    marginBottom: 16,
    lineHeight: 22,
  },
  infoContainer: {
    marginBottom: 20,
  },
  infoText: {
    fontSize: 14,
    color: Colors.GRAY_MEDIUM,
    marginBottom: 8,
    lineHeight: 20,
  },
  cancelButton: {
    backgroundColor: Colors.RED,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
});

const mapStateToProps = (state: any) => ({
  user: state.user,
});

const actions = {
  cancelCoolingOffRequest,
  checkCoolingOffStatusRequest,
};

export default connect(mapStateToProps, actions)(CoolingOffPeriod);
