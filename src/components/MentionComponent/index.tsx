import {Avatar, Text} from '../';
import {ButtonView} from '..';
import {AppStyles, Colors} from '../../theme';
import {View} from 'react-native';
import {FOLLOWERS, ImprintMentions} from '../../types';
import {FC} from 'react';
import styles from './styles';
import _ from 'lodash';
import {
  MentionInput,
  MentionSuggestionsProps,
  mentionRegEx,
  replaceMentionValues,
} from 'react-native-controlled-mentions';
import {CREATE_IMPRINT} from '../../constants/StringConstants';

interface mentionsProps {
  postDescription: string;
  followersList: FOLLOWERS[];
  onChangePostDescription: (arg0: string) => void;
  mentionsList: ImprintMentions[];
  setMentionsList: React.Dispatch<React.SetStateAction<ImprintMentions[]>>;
}

const MentionComponent: React.FC<mentionsProps> = ({
  postDescription,
  followersList,
  onChangePostDescription,
  mentionsList,
  setMentionsList,
}) => {
  const renderSuggestions: (
    followers: FOLLOWERS[],
  ) => FC<MentionSuggestionsProps> =
    suggestions =>
    ({keyword, onSuggestionPress}) => {
      if (keyword == null) {
        return null;
      }
      const handleSuggestionPress = (mention: FOLLOWERS) => {
        const {id, userName} = mention;
        const newMentionList = [...mentionsList, {userId: id, userName}];
        const mentionObject = {id: mention.id, name: mention.displayName};
        onSuggestionPress(mentionObject);
        setMentionsList(newMentionList);
      };

      return (
        <View>
          {followersList
            .filter((item: FOLLOWERS) =>
              item.displayName
                .toLocaleLowerCase()
                .includes(keyword.toLocaleLowerCase()),
            )
            .map((item: FOLLOWERS) => (
              <ButtonView onPress={() => handleSuggestionPress(item)}>
                <View style={styles.userinfoContainer}>
                  <Avatar
                    image={item.avatarUrl}
                    style={styles.imageContainer}
                    imageStyle={styles.image}
                  />
                  <View style={AppStyles.alignStart}>
                    <Text
                      color={Colors.itemColors.titleColor}
                      textAlign="center"
                      style={AppStyles.mLeft10}
                      type="bold"
                      size={'buttonText'}>
                      {item.displayName}
                    </Text>
                    <Text
                      color={Colors.text.placeHolderTextColor}
                      style={AppStyles.mLeft10}
                      type="base"
                      size={'xSmall'}>
                      {item.userName ?? ''}
                    </Text>
                  </View>
                </View>
              </ButtonView>
            ))}
        </View>
      );
    };

  const renderMentionSuggestions = renderSuggestions(followersList);

  return (
    <MentionInput
      maxLength={200}
      scrollEnabled
      value={replaceMentionValues(postDescription, ({name}) => `@${name}`)}
      onChange={onChangePostDescription}
      partTypes={[
        {
          isInsertSpaceAfterMention: true,
          pattern: mentionRegEx,
          trigger: '@',
          renderSuggestions: renderMentionSuggestions,
          textStyle: styles.mentionsText,
        },
      ]}
      placeholder={CREATE_IMPRINT.LEAVE_IMPRINT}
      style={styles.inputStyle}
      placeholderTextColor={Colors.tabsTextColor.activeTabColor}
    />
  );
};

export default MentionComponent;
