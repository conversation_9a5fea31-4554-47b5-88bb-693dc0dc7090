import React, {useEffect, useRef} from 'react';
import {Animated, Easing, Linking, Text, View} from 'react-native';
import SplashScreen from 'react-native-splash-screen';
import {SPLASH_BG, SPLASH_LOGO} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';
import {useNavigation} from '@react-navigation/native';
import {SPLASH} from '../../../constants/StringConstants';
import Routes from '../../../constants/RouteConstants';
import {getOnBoardingStatus} from '../../../helpers/UserHelper';
import {
  startYotiSessionRequest,
  userInfoRequest,
} from '../../../actions/UserActions';
import {connect} from 'react-redux';
import YotiService from '../../../services/YotiService';
import {QUIZ_STATUS, RootState, SubscriptionStatus} from '../../../types';
import DataHandler from '../../../services/DataHandler';
import util from '../../../util';

interface userProps {
  startYotiSessionRequest: (callback: (res: any) => void) => void;
  userInfoRequest: (response: (res: any) => void) => void;
  user: RootState;
}

const Splash: React.FC<userProps> = ({
  user,
  userInfoRequest,
  startYotiSessionRequest,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const opacityAnimFade = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(200)).current;
  const navigation = useNavigation();

  const fadeInFromBottom = () => {
    return Animated.timing(translateYAnim, {
      toValue: 0.8,
      duration: 900, // Set to the same duration as the other animation
      easing: Easing.out(Easing.circle),
      useNativeDriver: true,
    });
  };

  const animate = () => {
    return Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        easing: Easing.out(Easing.circle), // Use easing function of your choice
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 1000,
        easing: Easing.out(Easing.circle), // Use easing function of your choice
        useNativeDriver: true,
      }),
    ]);
  };

  const fadeOut = () => {
    Animated.timing(opacityAnimFade, {
      toValue: 0,
      duration: 250,
      easing: Easing.out(Easing.circle), // Use easing function of your choice
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    SplashScreen.hide();

    // Initialize Yoti deep link listener
    const yotiService = YotiService.getInstance();
    const removeListener = yotiService.initializeDeepLinkListener();

    const combinedAnimation = Animated.parallel([
      fadeInFromBottom(),
      animate(),
    ]);

    combinedAnimation.start(async () => {
      setTimeout(async () => {
        fadeOut();
        const initialRouteName = await getInitialRouteName();
        if (initialRouteName) {
          (navigation.navigate as (route: string) => void)(initialRouteName);
        }
      }, 1500);
    });

    return () => {
      removeListener();
    };
  }, []);

  const scale = {
    transform: [{scale: scaleAnim}],
    opacity: opacityAnim,
  };

  const scaleOut = {
    opacity: opacityAnimFade,
  };

  const translateY = {
    transform: [{translateY: translateYAnim}],
  };

  const getInitialRouteName = async () => {
    const onBoardingStatus = await getOnBoardingStatus();

    if (!onBoardingStatus) {
      return Routes.ONBOARDING;
    } else {
      const getUserToken = DataHandler.getStore()?.getState().user.access_token;

      if (getUserToken) {
        try {
          const res: any = await new Promise((resolve, reject) => {
            userInfoRequest((response: any) => {
              resolve(response);
            });
          });

          if (res && res?.systemActionForImprint?.actionType === 'SUSPEND') {
            return (navigation.navigate as (route: string) => void)(
              Routes.LOGIN,
            );
          }
          // Check if age verification is needed
          if (res && YotiService.needsAgeVerification(res)) {
            startYotiSessionRequest(yotiRes => {
              if (yotiRes?.shareUrl) {
                const yotiService = YotiService.getInstance();
                yotiService.openYotiApp(yotiRes.shareUrl, yotiRes.qrCode?.uri);
              }
            });
            return Routes.HOME_TABS; // Return to prevent further navigation
          }

          if (
            res.isOnboarded &&
            res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
          ) {
            if (
              ![
                SubscriptionStatus.Inactive,
                SubscriptionStatus.Incomplete,
                SubscriptionStatus.Canceled,
              ].includes(res.subscriptionStatus)
            ) {
              // If the user has a valid subscription status, navigate to HOME_TABS
              (navigation.navigate as (route: string) => void)(
                Routes.HOME_TABS,
              );
            } else {
              (navigation.navigate as (route: string) => void)(
                Routes.HOME_TABS,
              );
            }
          } else {
            if (res.avatarUrl === null) {
              (navigation.navigate as (route: string, params: any) => void)(
                Routes.PROFILE_SETUP,
                {personalData: res},
              );
            } else if (res.profile.dob === null) {
              navigation.navigate as (route: string, params: any) => void,
                (Routes.PROFILE_SETUP, {personalData: res});
            } else if (res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED) {
              (navigation.navigate as (route: string) => void)(
                Routes.QUIZ_FLOW,
              );
            } else {
              return (
                navigation.navigate as (route: string, params: any) => void
              )(Routes.PROFILE_SETUP, {personalData: ''});
            }
          }
        } catch (error) {
          return Routes.LOGIN;
        }
      } else {
        return Routes.LOGIN;
      }
    }
  };

  return (
    <View>
      <View style={styles.container}>
        <SPLASH_BG />
      </View>
      <View style={styles.animationContainer}>
        <Animated.View style={[scale, scaleOut]}>
          <SPLASH_LOGO />
        </Animated.View>
      </View>
      <Animated.View style={[translateY, scaleOut, styles.textContainerStyle]}>
        <Text style={styles.title}>{SPLASH.IMPRINT_LIVE}</Text>
        <Text style={styles.text}>{SPLASH.VALUE_BASED}</Text>
      </Animated.View>
    </View>
  );
};

const mapStateToProps = ({user}: RootState) => ({user});

const actions = {userInfoRequest, startYotiSessionRequest};

export default connect(mapStateToProps, actions)(Splash);
