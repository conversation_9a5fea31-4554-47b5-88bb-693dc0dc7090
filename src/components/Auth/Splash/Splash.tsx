import React, {useEffect, useRef, useState} from 'react';
import {Animated, Easing, Linking, Text, View} from 'react-native';
import SplashScreen from 'react-native-splash-screen';
import {SPLASH_BG, SPLASH_LOGO} from '../../../constants/AssetSVGConstants';
import {styles} from './styles';
import {useNavigation} from '@react-navigation/native';
import {SPLASH} from '../../../constants/StringConstants';
import Routes from '../../../constants/RouteConstants';
import {getOnBoardingStatus} from '../../../helpers/UserHelper';
import {
  startYotiSessionRequest,
  updateDobRequest,
  userInfoRequest,
  userLogout,
} from '../../../actions/UserActions';
import {connect} from 'react-redux';
import YotiService from '../../../services/YotiService';
import {AgeVerificationModal} from '../../index';
import {QUIZ_STATUS, RootState, SubscriptionStatus} from '../../../types';
import DataHandler from '../../../services/DataHandler';
import util from '../../../util';

interface userProps {
  updateDobRequest: (payload: any, callback: (res: any) => void) => void;
  startYotiSessionRequest: (callback: (res: any) => void) => void;
  userInfoRequest: (response: (res: any) => void) => void;
  user: RootState;
  userLogout: () => void;
}

const Splash: React.FC<userProps> = ({
  user,
  userInfoRequest,
  startYotiSessionRequest,
  userLogout,
  updateDobRequest,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const opacityAnimFade = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(200)).current;
  const navigation = useNavigation();

  // Age verification modal state
  const [showAgeVerificationModal, setShowAgeVerificationModal] =
    useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null,
  );

  const fadeInFromBottom = () => {
    return Animated.timing(translateYAnim, {
      toValue: 0.8,
      duration: 900, // Set to the same duration as the other animation
      easing: Easing.out(Easing.circle),
      useNativeDriver: true,
    });
  };

  const animate = () => {
    return Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        easing: Easing.out(Easing.circle), // Use easing function of your choice
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 1000,
        easing: Easing.out(Easing.circle), // Use easing function of your choice
        useNativeDriver: true,
      }),
    ]);
  };

  const fadeOut = () => {
    Animated.timing(opacityAnimFade, {
      toValue: 0,
      duration: 250,
      easing: Easing.out(Easing.circle), // Use easing function of your choice
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    SplashScreen.hide();

    // Initialize Yoti deep link listener
    const yotiService = YotiService.getInstance();
    const removeListener = yotiService.initializeDeepLinkListener();

    const combinedAnimation = Animated.parallel([
      fadeInFromBottom(),
      animate(),
    ]);

    combinedAnimation.start(async () => {
      setTimeout(async () => {
        fadeOut();
        const initialRouteName = await getInitialRouteName();
        if (initialRouteName) {
          (navigation.navigate as (route: string) => void)(initialRouteName);
        }
      }, 1500);
    });

    return () => {
      removeListener();
    };
  }, []);

  const scale = {
    transform: [{scale: scaleAnim}],
    opacity: opacityAnim,
  };

  const scaleOut = {
    opacity: opacityAnimFade,
  };

  const translateY = {
    transform: [{translateY: translateYAnim}],
  };

  const handleAgeVerificationComplete = async () => {
    setShowAgeVerificationModal(false);

    // Re-run the initialization logic after age verification
    const initialRouteName = await getInitialRouteName();
    if (initialRouteName) {
      (navigation.navigate as (route: string) => void)(initialRouteName);
    }
  };

  const getInitialRouteName = async () => {
    const onBoardingStatus = await getOnBoardingStatus();

    if (!onBoardingStatus) {
      return Routes.ONBOARDING;
    } else {
      const getUserToken = DataHandler.getStore()?.getState().user.access_token;

      if (getUserToken) {
        try {
          const res: any = await new Promise((resolve, reject) => {
            userInfoRequest((response: any) => {
              resolve(response);
            });
          });

          if (res && res?.systemActionForImprint?.actionType === 'SUSPEND') {
            return (navigation.navigate as (route: string) => void)(
              Routes.LOGIN,
            );
          }
          // Check if age verification is needed
          // if (res && YotiService.needsAgeVerification(res)) {
          //   // Show age verification modal instead of navigating
          //   setShowAgeVerificationModal(true);
          //   return; // Don't navigate anywhere
          // }

          if (
            res.isOnboarded &&
            res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
          ) {
            if (
              ![
                SubscriptionStatus.Inactive,
                SubscriptionStatus.Incomplete,
                SubscriptionStatus.Canceled,
              ].includes(res.subscriptionStatus)
            ) {
              // If the user has a valid subscription status, navigate to HOME_TABS
              (navigation.navigate as (route: string) => void)(
                Routes.HOME_TABS,
              );
            } else {
              (navigation.navigate as (route: string) => void)(
                Routes.HOME_TABS,
              );
            }
          } else {
            if (res.avatarUrl === null) {
              (navigation.navigate as (route: string, params: any) => void)(
                Routes.PROFILE_SETUP,
                {personalData: res},
              );
            } else if (res.profile.dob === null) {
              navigation.navigate as (route: string, params: any) => void,
                (Routes.PROFILE_SETUP, {personalData: res});
            } else if (res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED) {
              (navigation.navigate as (route: string) => void)(
                Routes.QUIZ_FLOW,
              );
            } else {
              return (
                navigation.navigate as (route: string, params: any) => void
              )(Routes.PROFILE_SETUP, {personalData: ''});
            }
            if (
              res.isOnboarded === true &&
              YotiService.needsAgeVerification(res)
            ) {
              // Show age verification modal instead of navigating
              setShowAgeVerificationModal(true);
              return; // Don't navigate anywhere
            }
          }
        } catch (error) {
          return Routes.LOGIN;
        }
      } else {
        return Routes.LOGIN;
      }
    }
  };

  return (
    <View>
      <View style={styles.container}>
        <SPLASH_BG />
      </View>
      <View style={styles.animationContainer}>
        <Animated.View style={[scale, scaleOut]}>
          <SPLASH_LOGO />
        </Animated.View>
      </View>
      <Animated.View style={[translateY, scaleOut, styles.textContainerStyle]}>
        <Text style={styles.title}>{SPLASH.IMPRINT_LIVE}</Text>
        <Text style={styles.text}>{SPLASH.VALUE_BASED}</Text>
      </Animated.View>

      <AgeVerificationModal
        logout={() => {
          console.log('logout');
          // navigation.navigate(Routes.LOGIN as never);
          // setShowAgeVerificationModal(false);
          util.handleUserLogout(userLogout, navigation);
          setTimeout(() => {
            setShowAgeVerificationModal(false);
          }, 500);
        }}
        visible={showAgeVerificationModal}
        onVerificationComplete={handleAgeVerificationComplete}
        onStartVerification={startYotiSessionRequest}
        userInfoRequest={userInfoRequest}
        updateDobRequest={updateDobRequest}
      />
    </View>
  );
};

const mapStateToProps = ({user}: RootState) => ({user});

const actions = {
  userInfoRequest,
  startYotiSessionRequest,
  userLogout,
  updateDobRequest,
};

export default connect(mapStateToProps, actions)(Splash);
