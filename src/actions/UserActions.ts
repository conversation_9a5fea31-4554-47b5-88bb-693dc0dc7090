import {
  ILoginPayload,
  ISetupProfilePayload,
  IVerifyUserNamePayload,
  ISocialPayload,
  UserInfo,
  ISearchPayload,
  IMedia_CheckIn,
  ITagMedia,
  IRespondTag,
  ISubmitQuizPayload,
  IPurchasedPayload,
  IBillingPayload,
  ITransparencyPayload,
} from '../types';

import {
  APPLE_SIGN_IN,
  GOOGLE_SIGN_IN,
  USER_SIGN_IN,
  USER_SIGNUP,
  USER_FRIENDS_SUGGESTION,
  FOLLOW_FRIENDS,
  VERIFY_USERNAME,
  SETUP_PROFILE,
  USER_INFO,
  SEARCH_USER,
  USER_LOGOUT,
  MEDIA_CHECK_IN_VIEW,
  REFRESH_PAGE,
  TAG_MEDIA,
  RESPOND_TO_TAG,
  PROFILE_QUIZ,
  SUBMIT_PROFILE_QUIZ,
  GET_PLANS,
  PURCHASE_SUBSCRIPTIONS,
  BIL<PERSON>ING_PORTAL,
  VALID_COUPON,
  APPLY_VOUCHER,
  SEND_OTP,
  VAL<PERSON>ATE_OTP,
  RESET_PASSWORD,
  APPEAL_DECISION,
  SET_SYSTEM_ACTION,
  BLOCK_USERS_LISTING,
  FRIENDS_SUGGESTIONS,
  GET_ZEN_DESK_TOKEN,
  VALIDATE_INVITE_CODE,
  GET_PRO_PLANS,
  UPDATE_USER_SUBSCRIPTION,
  CLEAR_LOCAL_SUBSCRIPTION_UPDATE,
  VALIDATE_RECEIPT,
  ACCEPT_TERMS_CONDITIONS,
  START_YOTI_SESSION,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
  data?: any;
}

export function userSignInRequest(
  payload: ILoginPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: USER_SIGN_IN.REQUEST,
  };
}

export function userInfoRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: USER_INFO.REQUEST,
  };
}

export function userGoogleLoginRequest(
  payload: ISocialPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: GOOGLE_SIGN_IN.REQUEST,
  };
}

export function userFriendsSuggestionRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: USER_FRIENDS_SUGGESTION.REQUEST,
  };
}
export function followFriendsRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: FOLLOW_FRIENDS.REQUEST,
  };
}

export function userSignInSuccess(
  payload: ILoginPayload,
  response: any,
): Action {
  return {
    payload,
    response,
    type: USER_SIGN_IN.SUCCESS,
  };
}

export function userSignUPRequest(
  payload: ILoginPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: USER_SIGNUP.REQUEST,
  };
}

export function userSignUPSuccess(
  payload: ILoginPayload,
  response: any,
): Action {
  return {
    payload,
    response,
    type: USER_SIGNUP.SUCCESS,
  };
}

export function userInfoSuccess(response: UserInfo): Action {
  return {
    response,
    type: USER_INFO.SUCCESS,
  };
}

export function verifyUserNameRequest(
  payload: IVerifyUserNamePayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VERIFY_USERNAME.REQUEST,
  };
}

export function verifyUserNameSuccess(
  payload: IVerifyUserNamePayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VERIFY_USERNAME.SUCCESS,
  };
}

export function setupProfileRequest(
  payload: FormData,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: SETUP_PROFILE.REQUEST,
  };
}

export function setupProfileSuccess(
  payload: ISetupProfilePayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: SETUP_PROFILE.SUCCESS,
  };
}
export function userSearchRequest(
  payload: ISearchPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: SEARCH_USER.REQUEST,
  };
}

export function userMedia_CheckInRequest(
  payload: IMedia_CheckIn,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: MEDIA_CHECK_IN_VIEW.REQUEST,
  };
}
export function userLogout(): Action {
  return {
    type: USER_LOGOUT.SUCCESS,
  };
}

export function isRefreshPage(payload: boolean): Action {
  return {
    payload,
    type: REFRESH_PAGE,
  };
}

export function userTagMediaRequest(
  payload: ITagMedia,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: TAG_MEDIA.REQUEST,
  };
}

export function userRespondToTagMedia(
  payload: IRespondTag,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: RESPOND_TO_TAG.REQUEST,
  };
}

export function userProfileQuizRequest(
  payload: ISubmitQuizPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: PROFILE_QUIZ.REQUEST,
  };
}
export function userSubmitProfileQuiz(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: SUBMIT_PROFILE_QUIZ.REQUEST,
  };
}

export function getSubscriptionPlans(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_PLANS.REQUEST,
  };
}
export function getSubscriptionPlansSuccess(data: any): Action {
  return {
    data,
    type: GET_PLANS.SUCCESS,
  };
}

export function getPurchaseSubscriptionLink(
  payload: IPurchasedPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: PURCHASE_SUBSCRIPTIONS.REQUEST,
  };
}

export function getBillingPortalLink(
  payload: IBillingPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: BILLING_PORTAL.REQUEST,
  };
}

export function getValidCouponRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VALID_COUPON.REQUEST,
  };
}

export function getValidateCodeRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VALIDATE_INVITE_CODE.REQUEST,
  };
}

export function ApplyVoucher(payload: boolean): Action {
  return {
    payload,
    type: APPLY_VOUCHER,
  };
}

export function sendOtpRequest(payload: any, responseCallback: any): Action {
  return {
    payload,
    responseCallback,
    type: SEND_OTP.REQUEST,
  };
}

export function validateOtpRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VALIDATE_OTP.REQUEST,
  };
}

export function resetPasswordRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: RESET_PASSWORD.REQUEST,
  };
}

export function appealDecisionRequest(
  payload: ITransparencyPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: APPEAL_DECISION.REQUEST,
  };
}

export function setSystemAction(payload?: any): Action {
  return {
    payload,
    type: SET_SYSTEM_ACTION,
  };
}

export function blockUserListingRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: BLOCK_USERS_LISTING.REQUEST,
  };
}

export function friendsSuggestionsRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: FRIENDS_SUGGESTIONS.REQUEST,
  };
}

export function getZendeskTokenRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_ZEN_DESK_TOKEN.REQUEST,
  };
}

export function getSubscriptionProPlans(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_PRO_PLANS.REQUEST,
  };
}

export function updateUserSubscriptionStatus(responseCallback: any): Action {
  return {
    responseCallback,
    type: UPDATE_USER_SUBSCRIPTION,
  };
}

export function validateReceiptRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VALIDATE_RECEIPT.REQUEST,
  };
}

export function clearLocalSubscriptionUpdate(): Action {
  return {
    type: CLEAR_LOCAL_SUBSCRIPTION_UPDATE,
  };
}

export function userAppleLoginRequest(
  payload: ISocialPayload,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: APPLE_SIGN_IN.REQUEST,
  };
}

export function acceptTermsAndConditionRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: ACCEPT_TERMS_CONDITIONS.REQUEST,
  };
}

export function startYotiSessionRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: START_YOTI_SESSION.REQUEST,
  };
}
