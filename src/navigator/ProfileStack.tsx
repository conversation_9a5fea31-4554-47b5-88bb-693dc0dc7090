import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Routes from '../constants/RouteConstants';
import {HomeTabs} from '../components/TabNavigator';
import UserProfile from '../containers/UserProfile';
import Profile from '../containers/Profile';
import {
  CheckIn,
  CreateImprint,
  Login,
  PaymentScreen,
  TimeLineMediaView,
  ViewMedia,
} from '../containers';
import MultipleMedia from '../containers/MultipleMedia';
import TypeOneEdit from '../components/About/TypeOneEdit';
import TypeTwoEdit from '../components/About/TypeTwoEdit';
import ReportScreen from '../components/ReportScreen';
import Subscription from '../containers/Subscription';
import InApp from '../containers/InApp';

const Stack = createNativeStackNavigator();

const ProfileStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {backgroundColor: 'white'},
        animation: 'simple_push',
      }}
      initialRouteName={Routes.PROFILE}>
      <Stack.Screen
        name={Routes.PROFILE}
        component={Profile}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.USER_PROFILE}
        component={UserProfile}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.LOGOUT}
        component={Login}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.TIME_LINE_MEDIA_VIEW}
        component={TimeLineMediaView}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.LOGIN}
        component={Login}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.CREATE_IMPRINT}
        component={CreateImprint}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.CHECK_IN}
        component={CheckIn}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.VIEW_MEDIA}
        component={ViewMedia}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.TYPE_ONE_EDIT}
        component={TypeOneEdit}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.TYPE_TWO_EDIT}
        component={TypeTwoEdit}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.MULTIPLE_MEDIA}
        component={MultipleMedia}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.REPORT_SCREEN}
        component={ReportScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.SUBSCRIPTION}
        component={Subscription}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={Routes.PAYMENT_SCREEN}
        component={PaymentScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={'INAPP'}
        component={InApp}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
export default ProfileStack;
