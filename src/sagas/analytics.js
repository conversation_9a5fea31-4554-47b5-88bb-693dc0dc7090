import {take, call, fork, put} from 'redux-saga/effects';
import {
  GET_TIME_OPTIONS,
  GET_VALUE_ANALYTICS,
  GET_VALUE_PORTFOLIO,
} from '../actions/ActionTypes';
import {
  callRequest,
  GET_VALUE_ANALYTICS as GET_VALUE_ANALYTICS_URL,
  GET_VALUE_PORTFOLIO as GET_VALUE_PORTFOLIO_URL,
  GET_TIME_OPTIONS as GET_TIME_OPTIONS_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {
  getTimeOptionsSuccess,
  getValueAnalyticsSuccess,
  getValuePortfolioSuccess,
} from '../actions/AnalyticsActions';
import {APIConstants} from '../constants/APIConstants';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getValueAnalytics() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_VALUE_ANALYTICS.REQUEST);
    const parameters = `${APIConstants.VALUE_ANALYTICS}?global=${payload.isGlobal}&value=${payload.valueType}`;
    try {
      const response = yield call(
        callRequest,
        GET_VALUE_ANALYTICS_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getValueAnalyticsSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getValuePortfolio() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_VALUE_PORTFOLIO.REQUEST);
    const parameters = `${APIConstants.VALUE_PORTFOLIO}?global=${payload.isGlobal}&type=${payload.type}`;
    try {
      const response = yield call(
        callRequest,
        GET_VALUE_PORTFOLIO_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getValuePortfolioSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getTimeOption() {
  while (true) {
    const {payload, responseCallback} = yield take(GET_TIME_OPTIONS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_TIME_OPTIONS_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getTimeOptionsSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getValueAnalytics);
  yield fork(getValuePortfolio);
  yield fork(getTimeOption);
}
