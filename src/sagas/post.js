import {take, call, fork} from 'redux-saga/effects';
import {USER_POST_IMPRINT, USER_UPDATE_IMPRINT} from '../actions/ActionTypes';
import {
  callRequest,
  USER_POST_IMPRINT as USER_POST_IMPRINT_URL,
  USER_UPDATE_IMPRINT as USER_UPDATE_IMPRINT_URL,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* postImrPint() {
  while (true) {
    const {payload, responseCallback} = yield take(USER_POST_IMPRINT.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_POST_IMPRINT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* updateImprint() {
  while (true) {
    const {payload, responseCallback} = yield take(USER_UPDATE_IMPRINT.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_UPDATE_IMPRINT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(postImrPint);
  yield fork(updateImprint);
}
