import {take, put, call, fork, takeLatest} from 'redux-saga/effects';
import {
  APPEAL_DECISION,
  FOL<PERSON>OW_FRIENDS,
  GOOGLE_SIGN_IN,
  USER_FRIENDS_SUGGESTION,
  USER_INFO,
  USER_SIGNUP,
  USER_SIGN_IN,
  VERIFY_USERNAME,
  SETUP_PROFILE,
  SEARCH_USER,
  MEDIA_CHECK_IN_VIEW,
  TAG_MEDIA,
  RESPOND_TO_TAG,
  PRO<PERSON>LE_QUIZ,
  SUBMIT_PROFILE_QUIZ,
  GET_PLANS,
  PURCHASE_SUBSCRIPTIONS,
  BILLING_PORTAL,
  VALID_COUPON,
  SEND_OTP,
  VALIDATE_OTP,
  RESET_PASSWORD,
  B<PERSON><PERSON>K_USERS_LISTING,
  FRIENDS_SUGGESTIONS,
  GET_ZEN_DESK_TOKEN,
  VALIDATE_INVITE_CODE,
  GET_PRO_PLANS,
  APPLE_SIGN_IN,
  VALIDATE_RECEIPT,
  // ✅ Cooling-off period actions
  START_COOLING_OFF,
  CANC<PERSON>_COOLING_OFF,
  CHECK_COOLING_OFF_STATUS,
} from '../actions/ActionTypes';
import {
  getSubscriptionPlansSuccess,
  userInfoSuccess,
  userSignInSuccess,
} from '../actions/UserActions';
import {
  APPLE_SIGN_IN as APPLE_SIGN_IN_URL,
  APPEAL_DECISION as APPEAL_DECISION_URL,
  SUBMIT_PROFILE_QUIZ as SUBMIT_PROFILE_QUIZ_URL,
  PROFILE_QUIZ as PROFILE_QUIZ_URL,
  RESPOND_TO_TAG as RESPOND_TO_TAG_URL,
  TAG_MEDIA as TAG_MEDIA_URL,
  USER_INFO as USER_INFO_URL,
  FOLLOW_FRIENDS as FOLLOW_FRIENDS_URL,
  USER_FRIENDS_SUGGESTION as USER_FRIENDS_SUGGESTION_URL,
  USER_SIGN_IN as USER_SIGN_IN_URL,
  GOOGLE_SIGN_IN as GOOGLE_SIGN_IN_URL,
  USER_SIGNUP as USER_SIGNUP_URL,
  VERIFY_USERNAME as VERIFY_USERNAME_URL,
  SETUP_PROFILE as SETUP_PROFILE_URL,
  SEARCH_USER as SEARCH_USER_URL,
  MEDIA_CHECK_IN_VIEW as MEDIA_CHECK_IN_VIEW_URL,
  PURCHASE_SUBSCRIPTIONS as PURCHASE_SUBSCRIPTIONS_URL,
  BILLING_PORTAL as BILLING_PORTAL_URL,
  GET_PLANS as GET_PLANS_URL,
  VALID_COUPON as VALID_COUPON_URL,
  SEND_OTP as SEND_OTP_URL,
  RESET_PASSWORD as RESET_PASSWORD_URL,
  VALIDATE_OTP as VALID_OTP_URL,
  BLOCK_USERS_LISTING as BLOCK_USERS_LISTING_URL,
  FRIENDS_SUGGESTIONS as FRIENDS_SUGGESTIONS_URL,
  GET_ZEN_DESK_TOKEN as GET_ZEN_DESK_TOKEN_URL,
  VALIDATE_INVITE_CODE as VALIDATE_INVITE_CODE_URL,
  GET_PRO_PLANS as GET_PRO_PLANS_URL,
  VALIDATE_RECEIPT as VALIDATE_RECEIPT_URL,
  // ✅ Cooling-off period URLs
  START_COOLING_OFF as START_COOLING_OFF_URL,
  CANCEL_COOLING_OFF as CANCEL_COOLING_OFF_URL,
  CHECK_COOLING_OFF_STATUS as CHECK_COOLING_OFF_STATUS_URL,
  callRequest,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import {APIConstants} from '../constants/APIConstants';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* signIn() {
  while (true) {
    const {payload, responseCallback} = yield take(USER_SIGN_IN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_SIGN_IN_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(userSignInSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* googleSignIn() {
  while (true) {
    const {payload, responseCallback} = yield take(GOOGLE_SIGN_IN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GOOGLE_SIGN_IN_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(userSignInSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      //   alert(Util.getErrorText(err.message));
    }
  }
}

function* appleSign() {
  while (true) {
    const {payload, responseCallback} = yield take(APPLE_SIGN_IN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        APPLE_SIGN_IN_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(userSignInSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      //   alert(Util.getErrorText(err.message));
    }
  }
}

function* getFriendsSuggestion() {
  while (true) {
    const {responseCallback} = yield take(USER_FRIENDS_SUGGESTION.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_FRIENDS_SUGGESTION_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        //    yield put(userSignInSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* followRequest() {
  while (true) {
    const {payload, responseCallback} = yield take(FOLLOW_FRIENDS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        FOLLOW_FRIENDS_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        //    yield put(userSignInSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getUserInfo() {
  while (true) {
    const {responseCallback} = yield take(USER_INFO.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_INFO_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, response);
        yield put(userInfoSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* signUp() {
  while (true) {
    const {payload, responseCallback} = yield take(USER_SIGNUP.REQUEST);
    try {
      const response = yield call(
        callRequest,
        USER_SIGNUP_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        // commented due to not getting user info after signup
        //  yield put(userSignInSuccess(payload, response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* verifyUserName() {
  while (true) {
    const {payload, responseCallback} = yield take(VERIFY_USERNAME.REQUEST);
    try {
      const response = yield call(
        callRequest,
        VERIFY_USERNAME_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* setupProfile() {
  while (true) {
    const {payload, responseCallback} = yield take(SETUP_PROFILE.REQUEST);
    try {
      const response = yield call(
        callRequest,
        SETUP_PROFILE_URL,
        payload,
        '',
        {'Content-Type': 'multipart/form-data'},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}
function* getSearchUser(action) {
  const {payload, responseCallback} = action;
  const parameters = `${APIConstants.SEARCH_USER}?display name=${payload.displayName}`;
  try {
    const response = yield call(
      callRequest,
      SEARCH_USER_URL,
      payload,
      parameters,
      {},
      ApiSauce,
    );
    if (response) {
      if (responseCallback) responseCallback(response, null);
    } else {
      if (responseCallback) responseCallback(response.data, null);
      alert('Something went wrong');
    }
  } catch (err) {
    if (responseCallback) responseCallback(null, err);
    alert(Util.getErrorText(err.message));
  }
}

function* mediaChcKinView() {
  while (true) {
    const {payload, responseCallback} = yield take(MEDIA_CHECK_IN_VIEW.REQUEST);
    const parameters = `${APIConstants.MEDIA_CHECK_IN_VIEW}${payload.checkin}`;
    try {
      const response = yield call(
        callRequest,
        MEDIA_CHECK_IN_VIEW_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* tagMedia() {
  while (true) {
    const {payload, responseCallback} = yield take(TAG_MEDIA.REQUEST);
    const parameters = `${APIConstants.TAG_MEDIA}?type=${payload.type}&page=${payload.page}`;
    try {
      const response = yield call(
        callRequest,
        TAG_MEDIA_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* respondToTag() {
  while (true) {
    const {payload, responseCallback} = yield take(RESPOND_TO_TAG.REQUEST);
    const parameters = `${APIConstants.RESPOND_TO_TAG}?imprintId=${payload.imprintId}&approve=${payload.approve}&notificationId=${payload.notificationId}`;
    try {
      const response = yield call(
        callRequest,
        RESPOND_TO_TAG_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}
function* userProfileQuiz() {
  while (true) {
    const {payload, responseCallback} = yield take(PROFILE_QUIZ.REQUEST);
    const parameters = `${APIConstants.PROFILE_QUIZ}page=${payload.page}&limit=${payload.limit}`;
    try {
      const response = yield call(
        callRequest,
        PROFILE_QUIZ_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}
function* submitQuiz() {
  while (true) {
    const {payload, responseCallback} = yield take(SUBMIT_PROFILE_QUIZ.REQUEST);
    try {
      const response = yield call(
        callRequest,
        SUBMIT_PROFILE_QUIZ_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getSubscriptionPlan() {
  while (true) {
    const {responseCallback} = yield take(GET_PLANS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_PLANS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getSubscriptionPlansSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getProPlans() {
  while (true) {
    const {responseCallback} = yield take(GET_PRO_PLANS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_PRO_PLANS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
        yield put(getSubscriptionPlansSuccess(response));
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* purchaseSubscription() {
  while (true) {
    const {payload, responseCallback} = yield take(
      PURCHASE_SUBSCRIPTIONS.REQUEST,
    );
    const parameters = `${APIConstants.PURCHASE_SUBSCRIPTIONS}/${payload?.lookupKey}/${payload?.customerId}/${payload.couponId}`;

    try {
      const response = yield call(
        callRequest,
        PURCHASE_SUBSCRIPTIONS_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      //  alert(Util.getErrorText(err.message));
    }
  }
}

function* generateBillingPortalLink() {
  while (true) {
    const {payload, responseCallback} = yield take(BILLING_PORTAL.REQUEST);
    const parameters = `${APIConstants.BILLING_PORTAL}/${payload?.customerId}`;

    try {
      const response = yield call(
        callRequest,
        BILLING_PORTAL_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* validateCoupon() {
  while (true) {
    const {payload, responseCallback} = yield take(VALID_COUPON.REQUEST);
    const parameters = `${APIConstants.VALID_COUPON}/${payload?.code}`;

    try {
      const response = yield call(
        callRequest,
        VALID_COUPON_URL,
        {},
        parameters,
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* sendOtp() {
  while (true) {
    const {payload, responseCallback} = yield take(SEND_OTP.REQUEST);

    try {
      const response = yield call(
        callRequest,
        SEND_OTP_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* validateOtp() {
  while (true) {
    const {payload, responseCallback} = yield take(VALIDATE_OTP.REQUEST);

    try {
      const response = yield call(
        callRequest,
        VALID_OTP_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* resetPassword() {
  while (true) {
    const {payload, responseCallback} = yield take(RESET_PASSWORD.REQUEST);

    try {
      const response = yield call(
        callRequest,
        RESET_PASSWORD_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* appealDecision() {
  while (true) {
    const {payload, responseCallback} = yield take(APPEAL_DECISION.REQUEST);

    try {
      const response = yield call(
        callRequest,
        APPEAL_DECISION_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* blockUserListing() {
  while (true) {
    const {responseCallback} = yield take(BLOCK_USERS_LISTING.REQUEST);
    try {
      const response = yield call(
        callRequest,
        BLOCK_USERS_LISTING_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getFriendsSuggestions() {
  while (true) {
    const {responseCallback} = yield take(FRIENDS_SUGGESTIONS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        FRIENDS_SUGGESTIONS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getZendeskToken() {
  while (true) {
    const {responseCallback} = yield take(GET_ZEN_DESK_TOKEN.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_ZEN_DESK_TOKEN_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* validateInviteCode() {
  while (true) {
    const {payload, responseCallback} = yield take(
      VALIDATE_INVITE_CODE.REQUEST,
    );

    try {
      const response = yield call(
        callRequest,
        VALIDATE_INVITE_CODE_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* validateReceipt() {
  while (true) {
    const {payload, responseCallback} = yield take(VALIDATE_RECEIPT.REQUEST);

    try {
      const response = yield call(
        callRequest,
        VALIDATE_RECEIPT_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Receipt validation failed');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

// ✅ Cooling-off period sagas
function* startCoolingOff() {
  while (true) {
    const {payload, responseCallback} = yield take(START_COOLING_OFF.REQUEST);

    try {
      const response = yield call(
        callRequest,
        START_COOLING_OFF_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Failed to start cooling-off period');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* cancelCoolingOff() {
  while (true) {
    const {payload, responseCallback} = yield take(CANCEL_COOLING_OFF.REQUEST);

    try {
      const response = yield call(
        callRequest,
        CANCEL_COOLING_OFF_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Failed to cancel cooling-off period');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* checkCoolingOffStatus() {
  while (true) {
    const {payload, responseCallback} = yield take(
      CHECK_COOLING_OFF_STATUS.REQUEST,
    );

    try {
      const response = yield call(
        callRequest,
        CHECK_COOLING_OFF_STATUS_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Failed to check cooling-off status');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(signIn);
  yield fork(signUp);
  yield fork(getFriendsSuggestion);
  yield fork(followRequest);
  yield fork(verifyUserName);
  yield fork(setupProfile);
  yield fork(googleSignIn);
  yield fork(getUserInfo);
  yield fork(mediaChcKinView);
  yield fork(tagMedia);
  yield fork(respondToTag);
  yield fork(userProfileQuiz);
  yield fork(submitQuiz);
  yield fork(getSubscriptionPlan);
  yield fork(purchaseSubscription);
  yield fork(generateBillingPortalLink);
  yield fork(validateCoupon);
  yield fork(sendOtp);
  yield fork(validateOtp);
  yield fork(resetPassword);
  yield fork(appealDecision);
  yield fork(blockUserListing);
  yield fork(getFriendsSuggestions);
  yield fork(getZendeskToken);
  yield fork(validateInviteCode);
  yield fork(getProPlans);
  yield fork(validateReceipt);
  // ✅ Cooling-off period sagas
  yield fork(startCoolingOff);
  yield fork(cancelCoolingOff);
  yield fork(checkCoolingOffStatus);
  yield fork(appleSign);

  yield takeLatest(SEARCH_USER.REQUEST, getSearchUser);
}
