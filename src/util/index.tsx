// @flow
import {
  Platform,
  Alert,
  Linking,
  PermissionsAndroid,
  Image,
  Text,
} from 'react-native';
import moment from 'moment';
import {MessageBarManager} from 'react-native-message-bar';
import DataHandler from '../services/DataHandler';
var passwordValidator = require('password-validator');
import {LayoutAnimation} from 'react-native';

import {
  MESSAGE_TYPES,
  DISCARD_WARNING,
  STONES_DATA,
  gifMapping,
  PERMISSIONS,
} from '../constants';
import Geolocation from '@react-native-community/geolocation';
import {PERMISSION_MESSAGES, RUDI_MESSAGE} from '../constants/StringConstants';
import {
  INewsResponse,
  ITransformedNews,
  Message,
  QUIZ_STATUS,
  RootState,
  SubscriptionStatus,
  UserInfo,
} from '../types';
import DefaultImage from '../assets/icons/rudi.png';
import {Colors} from '../theme';
import {CommonActions} from '@react-navigation/native';
import Routes from '../constants/RouteConstants';
import {decode} from 'html-entities';
import {showToastMsg} from '../components/Alert';

var schema = new passwordValidator();
schema
  .is()
  .min(5)
  .is()
  .max(100)
  .has()
  .uppercase()
  .has()
  .lowercase()
  .has()
  .digits()
  .has()
  .not()
  .spaces()
  .has()
  .symbols();

class Util {
  keyExtractor = (item: Object, index: number) => index.toString();
  isPlatformAndroid() {
    return Platform.OS === 'android';
  }
  isValidURL(url: 'string') {
    const re =
      /^(http|https|fttp):\/\/|[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,6}(:[0-9]{1,5})?(\/.*)?$/;
    return re.test(url);
  }
  isEmailValid(email: string) {
    const re =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email);
  }
  isPasswordValid(password: string) {
    return schema.validate(password);
  }
  isValidName(name: string) {
    return /^[a-zA-Z '.-]*$/.test(name);
  }
  getValidImage(image: string) {
    if (typeof image === 'string' && this.isValidURL(image)) {
      return {uri: image};
    }
    // if (typeof image === "string" && !this.isValidURL(image)) {
    //   return require(image);
    // }
    return image;
  }

  topAlert(message: string, alertType = 'success') {
    MessageBarManager.showAlert({
      message,
      alertType,
    });
  }

  topAlertError(message: any, alertType = MESSAGE_TYPES.ERROR) {
    MessageBarManager.showAlert({
      message,
      alertType,
    });
  }

  capitalizeFirstLetter(string: string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  isRequiredMessage(field: string) {
    return `${this.capitalizeFirstLetter(field)} is required`;
  }

  isInvalidMessage(field: string) {
    return `Invalid ${this.capitalizeFirstLetter(field)}`;
  }

  getFormattedDateTime = (
    date: moment.MomentInput,
    format: string | undefined,
  ) => {
    if (date) return moment(date).format(format);
    return '';
  };

  isAgeBetween13And18(currentDate: Date, birthDate: Date) {
    const minDate = new Date(currentDate);
    minDate.setFullYear(minDate.getFullYear() - 13);
    const maxDate = new Date(currentDate);
    maxDate.setFullYear(maxDate.getFullYear() - 18);
    return birthDate >= maxDate && birthDate <= minDate;
  }

  isAgeBetween18(currentDate: Date, birthDate: Date) {
    const minDate = new Date(currentDate);
    minDate.setFullYear(minDate.getFullYear() - 0);

    const maxDate = new Date(currentDate);
    maxDate.setFullYear(maxDate.getFullYear() - 18);

    return birthDate >= maxDate && birthDate <= minDate;
  }

  getFormattedDate = (date: Date | undefined) => {
    const formattedDate =
      date?.getFullYear() +
      '-' +
      (date && date.getMonth() + 1) +
      '-' +
      date?.getDate();
    return formattedDate;
  };

  getMinimumDate(currentDate = new Date()) {
    const minDate = new Date(currentDate);
    minDate.setFullYear(minDate.getFullYear() - 13);
    return minDate;
  }

  getDateObjectFromString = (
    date: moment.MomentInput,
    format: boolean | undefined,
  ) => {
    if (date) return moment(date, format).toDate();
    return '';
  };

  showLoader = (
    instance: {
      state: {loading: any};
      setState: (arg0: {loading: boolean; loadingFor: string}) => void;
    },
    loadingFor = '',
  ) => {
    if (!instance.state.loading) {
      instance.setState({
        loading: true,
        loadingFor,
      });
    }
  };

  hideLoader = (instance: any, callback: any) => {
    if (instance.state.loading) {
      instance.setState(
        {
          loading: false,
          loadingFor: '',
        },
        callback,
      );
    }
  };

  getCurrentUserAccessToken() {
    return DataHandler.getStore()?.getState().user.access_token;
  }

  userIsServiceProvider() {
    return (
      DataHandler.getStore()?.getState().user.data.user_type ===
      'service provider'
    );
  }

  getErrorText(error: string | any[]) {
    if (error instanceof Array) {
      if (error.length > 0) return error[0];
    } else {
      return error;
    }
    return '';
  }

  getType = (input: string): string | undefined => {
    if (input.startsWith('image/')) {
      return 'Image';
    } else if (input.startsWith('video/')) {
      return 'Video';
    } else if (input.startsWith('application/')) {
      return 'Document';
    } else if (input.startsWith('map/')) {
      return 'Map';
    } else if (/\.(pdf|docx)$/.test(input)) {
      return 'Document';
    } else {
      return 'Audio';
    }
  };

  discardAlert(onYesPress: any) {
    Alert.alert(
      'Discard?',
      DISCARD_WARNING as string,
      [
        {text: 'Yes', onPress: onYesPress},
        {text: 'No', style: 'cancel'},
      ],

      {cancelable: true},
    );
  }

  shoutOutAlert(onYesPress: any) {
    Alert.alert(
      'Are You Sure?',
      'Are you sure you want to move this Imprint from Personal Tribe to Global?',
      [
        {text: 'Done', onPress: onYesPress},
        {text: 'Cancel', style: 'cancel'},
      ],
      {cancelable: true},
    );
  }

  isNumber(val: string) {
    return /^\d+$/.test(val);
  }

  workInProgress() {
    MessageBarManager.showAlert({
      message: 'Work in progress',
      alertType: 'info',
    });
  }

  commingSoon() {
    MessageBarManager.showAlert({
      message: 'Coming soon',
      alertType: 'info',
    });
  }

  openLinkInBrowser(url: string) {
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      }
    });
  }

  generateGetParameter(obj: {[x: string]: any}) {
    let final = '?';
    for (const key in obj) {
      final = `${final}${key}=${obj[key]}&`;
    }
    final = final.slice(0, -1);
    return final;
  }

  getTimeDifference = (date: string) => {
    const now = new Date();
    const pastDate = new Date(date);
    const timeDifference = now.getTime() - pastDate.getTime();

    const seconds = Math.floor(timeDifference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
    }
  };
  getUpdatedTimeDifference = (date: string) => {
    const now = new Date();
    const pastDate = new Date(date);
    const timeDifference = now.getTime() - pastDate.getTime();

    const seconds = Math.floor(timeDifference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);

    // Helper function to format date as "MM DD YY HH:MM:SS"
    const formattedDate = (dateObj: Date) => {
      const month = dateObj.toLocaleString('en-US', {month: 'short'});
      const day = String(dateObj.getDate()).padStart(2, '0');
      const year = dateObj.getFullYear();
      let hour = dateObj.getHours();
      const minute = String(dateObj.getMinutes()).padStart(2, '0');
      const ampm = hour >= 12 ? 'pm' : 'am';
      hour = hour % 12 || 12;

      return `${month} ${day} ${year} ${hour}:${minute}${ampm}`;
    };

    if (days >= 365) {
      return formattedDate(pastDate);
    } else if (weeks >= 1) {
      return formattedDate(pastDate);
    } else if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
    }
  };

  async getCoordinates() {
    const self = this;
    return new Promise<{latitude: number; longitude: number} | void>(
      async function (resolve, reject) {
        let granted = undefined;
        if (self.isPlatformAndroid()) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: PERMISSION_MESSAGES.LOCATION_PERMISSION_TITLE,
              message: PERMISSION_MESSAGES.LOCATION_PERMISSION_DETAIL,
              buttonPositive: '',
            },
          );
          Geolocation.getCurrentPosition(
            (geo_success: {coords: {latitude: any; longitude: any}}) => {
              const {latitude, longitude} = geo_success.coords;
              resolve({latitude, longitude});
            },
            (geo_error: unknown) => {
              return reject(geo_error);
            },
            {
              enableHighAccuracy: true,
              timeout: 2000,
              maximumAge: 36000,
            },
          );
        } else {
          Geolocation.requestAuthorization(
            (success: unknown, error: any): any => {
              resolve();
            },
            console.error('err'),
          );

          Geolocation.getCurrentPosition(
            (geo_success: {coords: {latitude: any; longitude: any}}) => {
              const {latitude, longitude} = geo_success.coords;

              resolve({latitude, longitude});
            },
            (geo_error: unknown) => {
              reject(geo_error);
            },
            {
              enableHighAccuracy: true,
              timeout: 2000,
              maximumAge: 10000,
            },
          );
        }
      },
    );
  }
  removeFromKeys = (objectsArray: any[]) => {
    return objectsArray.map(obj => obj.fromUser);
  };
  addScoresToStonesData = (scores: any) => {
    const defaultScoreObject = {
      love: 0,
      life: 0,
      respect: 0,
      purpose: 0,
      safety: 0,
      laughter: 0,
    };

    const mergedScores = {...defaultScoreObject, ...scores};

    for (const key in mergedScores) {
      if (mergedScores[key] === null) {
        mergedScores[key] = 0;
      }
    }
    const stonesDataWithScores = STONES_DATA.map(stone => ({
      ...stone,
      score: mergedScores[stone.text],
    }));

    return stonesDataWithScores;
  };
  tranFormChatResponse(response: any[]) {
    const decodeContent = (text: string): string => {
      const urlDecode = (text: string): string => {
        try {
          return decodeURIComponent(text);
        } catch (error) {
          console.error('Error decoding URI component:', error);
          return text;
        }
      };

      const htmlDecode = (text: string): string => {
        return decode(text);
      };
      const urlDecoded = urlDecode(text);
      return htmlDecode(urlDecoded);
    };

    return response.map(message => ({
      _id: message.id,
      text: message.content
        ? decodeContent(message.content || '').replace(/[^\x20-\x7E‘’”]/g, '')
        : '',
      createdAt: message.createdAt,
      avatar: message.user.avatarUrl,
      isFlagged: message.isFlagged,
      user: {
        _id: message.user.id,
        name: message.user.displayName,
        avatar: message.user.avatarUrl,
        email: message.user.email,
      },
      image:
        message.attachmentType === 'GIF' && gifMapping[message.attachmentUrl]
          ? Image.resolveAssetSource(gifMapping[message.attachmentUrl]).uri
          : null,
    }));
  }

  htmlToPlainText(html: string) {
    // Create a temporary DOM element to use the browser's HTML parser
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get the text content from the temporary element
    let plainText = tempDiv.innerText || tempDiv.textContent || '';

    // Decode HTML entities (if needed)
    plainText = plainText
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&apos;/g, "'");

    // Trim any leading/trailing whitespace
    return plainText.trim();
  }

  transformRudiMessages(response: any[], user: UserInfo) {
    const DEFAULT_IMAGE = Image.resolveAssetSource(DefaultImage).uri;

    return response.map(message => ({
      _id: Math.random().toString(),
      text: message.content,
      createdAt: message.createdAt,

      news_articles: message.newsArticles ?? [],

      user: {
        _id:
          message.senderType === 'assistant'
            ? RUDI_MESSAGE.BOT_ID
            : message.senderId,
        name:
          message.senderType === 'assistant'
            ? RUDI_MESSAGE.BOT_NAME
            : user.displayName,
        avatar: message.senderType === 'user' ? user.avatarUrl : DEFAULT_IMAGE,
      },
    }));
  }

  tranFormNewsData(input: INewsResponse[]): ITransformedNews[] {
    return input.map(item => ({
      datePublished: item.datePublished,
      id: item.id,
      imageUrl: item.imageUrl,
      summary: item.body, // Using `body` as `summary`
      title: item.title,
      url: item.url,
    }));
  }

  formatTime = (timestamp: string): string => {
    const now = Date.now();
    const diff = now - Date.parse(timestamp);

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const month = Math.floor(days / 30);

    if (seconds < 60) {
      return 'Just now';
    } else if (minutes < 60) {
      return `${minutes} minutes ago`;
    } else if (hours < 24) {
      const formattedTime = new Date(timestamp).toLocaleTimeString([], {
        hour: 'numeric',
        minute: '2-digit',
      });
      return `Today at ${formattedTime}`;
    } else if (days < 7) {
      const formattedDate = new Date(timestamp).toLocaleDateString([], {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      });
      return `A week ago on ${formattedDate}`;
    } else if (month > 0) {
      const formattedDate = new Date(timestamp).toLocaleDateString([], {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      });
      return `A month ago on ${formattedDate}`;
    } else if (month > 12) {
      const formattedDate = new Date(timestamp).toLocaleDateString([], {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      });
      return `A year ago on ${formattedDate}`;
    } else {
      const formattedDate = new Date(timestamp).toLocaleDateString([], {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      });
      return `A week ago on ${formattedDate}`;
    }
  };

  resetToUserProfile = (navigation: any) => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: Routes.HOME_TABS,
            state: {
              index: 0,
              routes: [
                {
                  name: Routes.PROFILE_STACK,
                  state: {
                    index: 1,
                    routes: [
                      {name: Routes.PROFILE},
                      {name: Routes.USER_PROFILE},
                    ],
                  },
                },
              ],
            },
          },
        ],
      }),
    );
  };

  resetToSubscriptions = (navigation: any) => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: Routes.HOME_TABS,
            state: {
              index: 0,
              routes: [
                {
                  name: Routes.PROFILE_STACK,
                  state: {
                    index: 1,
                    routes: [
                      {name: Routes.PROFILE},
                      {name: Routes.SUBSCRIPTION},
                    ],
                  },
                },
              ],
            },
          },
        ],
      }),
    );
  };

  requestMicrophonePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: 'Microphone Permission',
          message:
            'This app needs access to your microphone to record audio and speech to text',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      return false;
      // console.warn(err);
    }
  };
  formatTimer = (timeInMs: any) => {
    const minutes = Math.floor(timeInMs / 1000 / 60);
    const seconds = Math.floor(timeInMs / 1000) % 60;
    return `${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // sanitizeFormFields = (text: any) => {
  //   const decodeHtmlEntities = (text: string | any[]) => {
  //     if (!text || !text.length) return text;
  //     try {
  //       const decodedURIComponent = decodeURIComponent(text);
  //       const textarea = document.createElement('textarea');
  //       textarea.innerHTML = decodedURIComponent;
  //       return textarea.value || '';
  //     } catch (error) {
  //       console.error(
  //         'Error decoding URI component:',
  //         error,
  //         'Input text:',
  //         text,
  //       );
  //       return text; // Return the original text if decoding fails
  //     }
  //   };

  //   if (!text || !text.length) return text;

  //   const harmfulPatterns = [
  //     /<script\b[^>]*>([\s\S]*?)<\/script>/gi,
  //     /javascript\s*:[^'">]+/gi,
  //     /vbscript\s*:[^'">]+/gi,
  //     /data\s*:[^'">]+/gi,
  //   ];

  //   let sanitized = String(decodeHtmlEntities(text));

  //   harmfulPatterns.forEach(pattern => {
  //     if (pattern.test(sanitized)) {
  //       console.log('Unsafe Content Detected');
  //       sanitized = sanitized.replace(pattern, '');
  //     }
  //   });

  //   sanitized = sanitized
  //     .replace(/</g, '&lt;')
  //     .replace(/>/g, '&gt;')
  //     .replace(/"/g, '&quot;')
  //     .replace(/'/g, '&#39;');

  //   return sanitized;
  // };

  decodeContent = (text: string): string => {
    const urlDecode = (text: string): string => {
      try {
        return decodeURIComponent(text);
      } catch (error) {
        console.error('Error decoding URI component:', error);
        return text;
      }
    };

    const htmlDecode = (text: string): string => {
      return decode(text);
    };
    const urlDecoded = urlDecode(text);
    return htmlDecode(urlDecoded);
  };

  decodeText = (text: string): string => {
    return decode(text);
  };

  handleUserLogout = (userLogout: () => void, navigation?: any) => {
    userLogout();
    navigation &&
      navigation.reset({
        index: 0,
        routes: [
          {
            name: Routes.AUTH_STACK as never,
            state: {routes: [{name: Routes.LOGIN as never}]},
          },
        ],
      });
  };

  formatMessage = (message: string) => {
    return message.split(/({{.*?}})/g).map((part, index) => {
      if (part.startsWith('{{') && part.endsWith('}}')) {
        return (
          <Text key={index} style={{fontWeight: 'bold'}}>
            {part.slice(2, -2)} BADGE
          </Text>
        );
      }
      return <Text key={index}>{part}</Text>;
    });
  };

  formatUsername = (message: string) => {
    return message.split(/({{.*?}})/g).map((part, index) => {
      if (part.startsWith('{{') && part.endsWith('}}')) {
        return (
          <Text key={index} style={{fontWeight: 'bold'}}>
            {part.slice(2, -2)}
          </Text>
        );
      }
      return <Text key={index}>{part}</Text>;
    });
  };

  decodeJWT = (token: string): any | null => {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      const base64 = parts[1]
        .replace(/-/g, '+')
        .replace(/_/g, '/')
        .padEnd(parts[1].length + ((4 - (parts[1].length % 4)) % 4), '=');

      const jsonPayload = atob(base64);
      return JSON.parse(jsonPayload);
    } catch (e) {
      console.error('Failed to decode token:', e);
      return null;
    }
  };

  extractBracketText = (message: string): string => {
    const match = message.match(/{{(.*?)}}/);
    return match ? match[1] : '';
  };

  handleUserInfo = (
    userInfoRequest: (response: (res: any) => void) => void,
    navigation: any,
    setLoading: (loading: boolean) => void,
    setShowAgeVerificationModal?: (visible: boolean) => void,
  ) => {
    setLoading(true);

    userInfoRequest(res => {
      if (res) {
        if (
          res.isOnboarded &&
          res.onboardingQuizStatus === QUIZ_STATUS.COMPLETED
        ) {
          if (
            ![
              SubscriptionStatus.Inactive,
              SubscriptionStatus.Incomplete,
              SubscriptionStatus.Canceled,
            ].includes(res.subscriptionStatus)
          ) {
            (navigation.navigate as (route: string) => void)(Routes.HOME_TABS);
          } else {
            this.resetToUserProfile(navigation);
          }
        } else {
          if (res.isOnboarded === false) {
            (navigation.navigate as (route: string, params: any) => void)(
              Routes.PROFILE_SETUP,
              {personalData: res},
            );
          } else if (res.isOnboarded === true && res.ageVerified === false) {
            if (setShowAgeVerificationModal) {
              setTimeout(() => {
                setShowAgeVerificationModal(true);
              }, 500);
            }
            return;
          } else if (res.onboardingQuizStatus === QUIZ_STATUS.NOT_ATTEMPTED) {
            (navigation.navigate as (route: string) => void)(Routes.QUIZ_FLOW);
          } else {
            return (
              navigation.navigate as (route: string, params: any) => void
            )(Routes.PROFILE_SETUP, {personalData: ''});
          }
        }
      } else {
        showToastMsg('Something went wrong');
      }
      setLoading(false);
    });
  };
}
export const sanitizeFormFields = (text: string) => {
  if (!text || !text.length) return text;

  const harmfulPatterns = [
    /<script\b[^>]*>([\s\S]*?)<\/script>/gi,
    /javascript\s*:[^'">]+/gi,
    /vbscript\s*:[^'">]+/gi,
    /data\s*:[^'">]+/gi,
    /<iframe\b[^>]*>([\s\S]*?)<\/iframe>/gi,
    /<iframe\b[^>]*\/?>/gi,
  ];

  let sanitized = String(decodeHtmlEntities(text));

  harmfulPatterns.forEach(pattern => {
    if (pattern.test(sanitized)) {
      console.log('Unsafe Content Detected');
      sanitized = sanitized.replace(pattern, '');
    }
  });

  sanitized = sanitized
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');

  return sanitized;
};

export const decodeHtmlEntities = (text: string | any[]) => {
  if (!text || !text.length) return text;
  try {
    const decodedURIComponent = decodeURIComponent(text);
    const textarea = document.createElement('textarea');
    textarea.innerHTML = decodedURIComponent;
    return textarea.value || '';
  } catch (error) {
    console.error('Error decoding URI component:', error, 'Input text:', text);
    return text; // Return the original text if decoding fails
  }
};

export const isUserSubscribed = (user: RootState['user']): boolean => {
  const status = user?.userInfo?.subscriptionStatus;

  return (
    status === SubscriptionStatus.ACTIVE ||
    status === SubscriptionStatus.CancellationScheduled ||
    status === SubscriptionStatus.Trialing
  );
};

export const isContentSafe = text => {
  const harmfulPatterns = [
    /<script\b[^>]*>([\s\S]*?)<\/script>/gi,
    /javascript\s*:[^'">]+/gi,
    /vbscript\s*:[^'">]+/gi,
    /data\s*:[^'">]+/gi,
    /<iframe\b[^>]*>([\s\S]*?)<\/iframe>/gi,
    /<iframe\b[^>]*\/?>/gi,
  ];

  const sanitized = String(decodeHtmlEntities(text));
  return !harmfulPatterns.some(pattern => pattern.test(sanitized));
};

export const easeInEaseOut = () => {
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
};

export const hasPermission = (
  PERMISSIONS: Record<string, string> = {},
  permissionToCheck: string,
) => {
  return Object.values(PERMISSIONS).includes(permissionToCheck);
};
export const canCreateCheckin = (activePermissions: {
  permissions: Record<string, string> | undefined;
}) => {
  return hasPermission(
    activePermissions.permissions,
    PERMISSIONS.CHECKIN_CREATE,
  );
};
export const canUploadArticle = (activePermissions: {
  permissions: Record<string, string> | undefined;
}) => {
  return hasPermission(
    activePermissions.permissions,
    PERMISSIONS.IMPRINT_UPLOAD_ARTICLE,
  );
};

export const canUploadMedia = (activePermissions: {
  permissions: Record<string, string> | undefined;
}) => {
  return hasPermission(
    activePermissions.permissions,
    PERMISSIONS.IMPRINT_UPLOAD_MEDIA,
  );
};

export const canUploadImage = (activePermissions: {
  permissions: Record<string, string> | undefined;
}) => {
  return hasPermission(
    activePermissions.permissions,
    PERMISSIONS.IMPRINT_UPLOAD_IMAGE,
  );
};

export const canUploadVideo = (activePermissions: {
  permissions: Record<string, string> | undefined;
}) => {
  return hasPermission(
    activePermissions.permissions,
    PERMISSIONS.IMPRINT_UPLOAD_VIDEO,
  );
};

export const normalizePlanId = (id: string) => {
  if (id === 'Basic_plans' || id === 'basic_plan') return 'basic_plan_monthly';
  if (id === 'basic_plan_annual') return 'basic_plan_annual';
  return id;
};

export const canUseSpeechToText = (activePermissions: {
  permissions: Record<string, string> | undefined;
}) => {
  return hasPermission(
    activePermissions.permissions,
    PERMISSIONS.IMPRINT_UPLOAD_SPEECH,
  );
};

export default new Util();
