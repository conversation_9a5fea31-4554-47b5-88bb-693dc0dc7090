export enum ReactionType {
  ANGRY = 'ANGRY',
  LOVE = 'LOVE',
  SAD = 'SAD',
  SCARED = 'SCARED',
  VALUE_BADGE_ASSIGNED_LOVE = 'VALUE_BADGE_ASSIGNED_LOVE',
  RESPECT = 'RESPECT',
  PURPOSE = 'PURPOSE',
  SAFETY = 'SAFETY',
  SHOUT_OUT = 'Shout Out',
}

export enum moderationStatus {
  NONE = 'NONE',
  SUBMITTED = 'SUBMITTED',
  SUBMISSION_ERROR = 'SUBMISSION_ERROR',
  INCIDENT_REPORTED = 'INCIDENT_REPORTED',
  MODERATED = 'MODERATED',
}

export const CONTENT_TYPE_MAPPING = {
  IMPRINT: 'post',
  CONVERSATION_MESSAGE: 'chat',
  USER_REPORT: 'profile',
};

export const CHAT_BLOCK_MAPPING = {
  BAN_CONVERSATION: 'BAN_CONVERSATION',
  BAN_CONVERSATION_U18: 'BAN_CONVERSATION_U18',
};

export enum ageBucket {
  AMBER = 'AMBER',
  GREEN = 'GREEN',
  RED = 'RED',
  YELLOW = 'YELLOW',
}

export enum NotificationTypeEnum {
  COMMENT = 'COMMENT',
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  LAUGHTER = 'LAUGHTER',
  RESPECT = 'RESPECT',
  SMILE = 'SMILE',
  TEAR = 'TEAR',
  ANGRY = 'ANGRY',
  SHOCK = 'SHOCK',
  LOVE = 'LOVE',
  HAPPY = 'HAPPY',
  LIFE = 'LIFE',
  SAD = 'SAD',
  CARE = 'CARE',
  SPEAK_UP = 'SPEAK_UP',
  FOLLOW_REQUEST = 'FOLLOW_REQUEST',
  FOLLOW_REQUEST_ACCEPT = 'FOLLOW_REQUEST_ACCEPT',
  FOLLOW_REQUEST_REJECT = 'FOLLOW_REQUEST_REJECT',
  ARTICLE_VERIFICATION_REQUESTED = 'ARTICLE_VERIFICATION_REQUESTED',
  ARTICLE_VERIFICATION_REJECTED = 'ARTICLE_VERIFICATION_REJECTED',
  ARTICLE_VERIFICATION_ACCEPTED = 'ARTICLE_VERIFICATION_ACCEPTED',
  ARTICLE_VERIFICATION_SUCCESS = 'ARTICLE_VERIFICATION_SUCCESS',
  ARTICLE_VERIFICATION_FAILURE = 'ARTICLE_VERIFICATION_FAILURE',
  TRIAL_PERIOD_ABOUT_TO_END = 'TRIAL_PERIOD_ABOUT_TO_END',
  IMPRINT_MEDIA_MENTION = 'IMPRINT_MEDIA_MENTION',
  IMPRINT_MENTION = 'IMPRINT_MENTION',
  CHECKOUT_QUOTE_JOKE = 'CHECKOUT_QUOTE_JOKE',
  CHECKOUT_GOOD_DEEDS = 'CHECKOUT_GOOD_DEEDS',
  DAILY_NEWS = 'DAILY_NEWS',
  IMPRINT_REJECTED_FOR_ALL_USERS = 'IMPRINT_REJECTED_FOR_ALL_USERS',
  IMPRINT_REJECTED_FOR_ALL_UNDER_18POLICY = 'IMPRINT_REJECTED_FOR_ALL_UNDER_18POLICY',
  IMPRINT_LIVE = 'IMPRINT_LIVE',
  UPDATED_VALUE_BADGE_ASSIGNED = 'UPDATED_VALUE_BADGE_ASSIGNED',
  VALUE_BADGE_ASSIGNED_LAUGHTER = 'VALUE_BADGE_ASSIGNED_LAUGHTER',
  VALUE_BADGE_ASSIGNED_SAFETY = 'VALUE_BADGE_ASSIGNED_SAFETY',
  VALUE_BADGE_ASSIGNED_PURPOSE = 'VALUE_BADGE_ASSIGNED_PURPOSE',
  VALUE_BADGE_ASSIGNED_RESPECT = 'VALUE_BADGE_ASSIGNED_RESPECT',
  VALUE_BADGE_ASSIGNED_SAD = 'VALUE_BADGE_ASSIGNED_SAD',
  VALUE_BADGE_ASSIGNED_LOVE = 'VALUE_BADGE_ASSIGNED_LOVE',
  USER_REPORTED_IMPRINT_REJECTED_FOR_UNDER_18_USERS = 'USER_REPORTED_IMPRINT_REJECTED_FOR_UNDER_18_USERS',
  CONTENT_MODERATED = 'CONTENT_MODERATED',
  IMPRINT_REJECTED_FOR_UNDER_18_USERS = 'IMPRINT_REJECTED_FOR_UNDER_18_USERS',
  USER_REPORTED_IMPRINT_LIVE = 'USER_REPORTED_IMPRINT_LIVE',
  USER_REPORTED_IMPRINT_DELETED = 'USER_REPORTED_IMPRINT_DELETED',
  INVESTIGATION_UPHELD_DECISION = 'INVESTIGATION_UPHELD_DECISION',
  INVESTIGATION_CONTENT_VIOLATES_GUIDELINES = 'INVESTIGATION_CONTENT_VIOLATES_GUIDELINES',
  INVESTIGATION_CONTENT_VIOLATES_GUIDELINES_UNDER_18 = 'INVESTIGATION_CONTENT_VIOLATES_GUIDELINES_UNDER_18',
  USER_ACCOUNT_SUSPENDED_YEARLY = 'USER_ACCOUNT_SUSPENDED_YEARLY',
  ACCOUNT_SUSPENDED = 'ACCOUNT_SUSPENDED',
  USER_ACCOUNT_SUSPENDED = 'USER_ACCOUNT_SUSPENDED',
  SUBSCRIPTION_CANCELLED_USER_BANNED = 'SUBSCRIPTION_CANCELLED_USER_BANNED',
  MESSAGING_RESTRICTED_ALL = 'MESSAGING_RESTRICTED_ALL',
  MESSAGING_RESTRICTED_UNDER18 = 'MESSAGING_RESTRICTED_UNDER18',
  INVESTIGATION_REPORTED_USER_IS_A_RISK = 'INVESTIGATION_REPORTED_USER_IS_A_RISK',
  CONTENT_REMOVAL = 'CONTENT_REMOVAL',
  NOK_REJECTED = 'NOK_REJECTED',
  NOK_REMINDER = 'NOK_REMINDER',
}
export enum SubscriptionStatus {
  INCOMPLETE = 'incomplete',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TRIALING = 'trialing',
}

export enum registrationSource {
  STRIPE = 'STRIPE',
  GOOGLE_PAY = 'GOOGLE_PAY',
  APPLE_PAY = 'APPLE_PAY',
}

export type ILoginPayload = {
  email: string;
  password: string;
};

export type ITransparencyPayload = {
  content_id: string;
  complex_type: string;
};

export type ISignUpPayload = {
  email: string;
  password: string;
  dob: Date | string;
  phone: string;
  countryCode: string;
};

export type IFollowPayload = {
  to: string;
};

export type ISocialPayload = {
  idToken: string;
};
export type ILoginResponse = {
  email: string;
  password: string;
};

export type ISignUp = {
  email: string;
  password: string;
};

export type IForgotPassword = {
  email: string;
};

export interface RootState {
  subscription: any;
  user: UserState;
  followers: FollowerState;
  generalReducer: generalState;
}
export interface UserState {
  data: Data;
  access_token: '';
  userInfo: UserInfo;
  isAppliedVoucher: boolean;
}
export interface generalState {
  isRefreshPage: boolean;
}
export interface FollowerState {
  data: FOLLOWERS[];
  followRequestData: FOLLOWERS[];
}
export interface Data {
  isEmailVerified: boolean;
  tokens: Tokens;
  userId: string;
}

export interface Tokens {
  accessToken: string;
  refreshToken: string;
}

export interface ITimeLinePayLoad {
  page: number;
  scores: [string];
}

export interface IFreeTextSearchPayload {
  query: string;
  limit: number;
  page: number;
  scores: string[];
}

export interface IVerifyUserNamePayload {
  userName: string;
}

export interface ISetupProfilePayload {
  userName: string;
  displayName: string;
  about: string;
  file: any;
  dob: string;
}

export interface IEditImagePayLoad {
  file: any;
}

export type IUnFollow = {
  follower?: string;
};

export type IBlockEntity = {
  follower?: string;
  status: boolean;
};

export type IEntertainmentContent = {
  type: string | undefined;
};

export type ISharePayload = {
  headline: string;
  description: string;
  isGlobal: boolean;
  type: string;
};

export type IRespondFollower = {
  requestID: string;
  isAccepted: boolean;
};

export interface ITimeLine {
  checkIn?: any;
  createdAt: string;
  description: string;
  followerRequestSent: boolean;
  id: string;
  imprintMedias: ImprintMedia[];
  isFlagged?: boolean;
  isFollower: boolean;
  isGlobal: boolean;
  mentions: any[];
  moderationResponse?: string[][];
  moderationStatus: moderationStatus;
  reactionsCount?: number;
  score?: Score;
  speakUpCount?: number;
  user: User;
  userId: string;
  userImprintInteractions: UserImprintInteractions[];
  verificationRequest?: VerificationRequest;
  ageBucket?: ageBucket;
  headline: string | null;
  isApproved?: boolean;
}

export interface UserImprintInteractions {
  id: string;
  imprintId: string;
  userId: string;
  isBookmarked: boolean;
  isRedFlagged: boolean;
  redFlagReason: any;
  isHide: boolean;
  reaction: string;
  createdAt: string;
  updatedAt: string;
  user?: User;
  redirectUrl?: string;
}

export interface Score {
  laughter?: any;
  life?: any;
  love?: any;
  purpose?: any;
  respect?: any;
  safety?: any;
}

export interface User {
  avatarUrl: string;
  email: string;
  id: string;
  userName: string;
  displayName?: string;
}

export interface VerificationRequest {
  createdAt: string;
  requestedBy: string;
  verificationStatus: string;
}

export interface PostReactionPayload {
  imprintId?: string;
  userId?: string;
  isRedFlagged?: boolean;
  isHide?: boolean;
  reaction?: any;
  isGlobal?: boolean;
  redFlagReason?: string | null;
  email?: string;
}

export interface UserInfo {
  isFollower?: boolean;
  followerRequestSent?: boolean;
  ageBucket: string;
  avatarUrl: any;
  bio: any;
  createdAt: string;
  displayName: any;
  email: string;
  id: string;
  isEmailVerified: boolean;
  isOnboarded: boolean;
  kinApprovalStatus: string;
  profile: Profile;
  onboardingQuizStatus: string;
  scores: Scores;
  stripeCustomerId: string;
  subscriptionId: any;
  subscriptionStatus: string;
  userId: string;
  userName: any;
  discountApplied: {availedBHDiscount: boolean};
  systemActionForUserReport: any;
  systemActionForImprint: any;
  systemActionForChat: any;
  isUserBlocked?: boolean;
  blockedImprints?: boolean;
  isGoogleLogin?: boolean;
  uuid?: string;
  registrationSource?: string;
  billingType: 'month' | 'year';
  productPurchaseName?: string;
  productPurchaseId?: string;
  EULA: boolean;
  termsAndConditions: boolean;
}

export interface Profile {
  about: any;
  countryCode: string;
  createdAt: string;
  dob: string;
  gender: any;
  id: string;
  phone: string;
  updatedAt: string;
  userId: string;
  what_i_stand_for: any;
  what_i_value: any;
}

export interface QuizResponse {
  status: string;
}

export interface Scores {
  laughter: number;
  life: number;
  love: number;
  purpose: number;
  respect: number;
  safety: number;
}

export interface IPostImprintPayload {
  description?: string;
  place?: string;
  lat?: string;
  long?: string;
  date?: Date;
  mediaQuantity: number;
  isCheckIn: boolean;
  isGlobal: boolean;
  verifyArticle?: {
    requestedBy: string;
  };
  imprintMentions?: ImprintMentions[];
}
export interface ImprintMentions {
  userId: string;
  userName: string;
}

export interface IVerifyArticle {
  verifyArticle: {
    requestedBy: string;
  };
}
export interface ImprintMedia {
  filename?: string;
  file?: string;
  mime?: string;
  sourceURL?: string | undefined;
  createdAt?: string;
  description?: any;
  id?: string;
  imprintId?: string;
  isUploaded?: boolean;
  name?: any;
  thumbUrl?: string;
  type?: string;
  updatedAt?: string;
  url?: string;
  path?: string;
}

export interface ImprintCheckIn {
  place: string;
  lat: string;
  long: string;
  date: Date;
  isCheckIn: boolean;
  checkInImage?: string;
}
export interface MediaItem {
  id?: string;
  name?: string;
  sourceURL?: string;
  mime?: string;
  filename?: string;
  path?: string;
  url?: string;
  file?: string;
}

export interface MediaInfo {
  id: string;
  presigned_url: string;
}

export interface MediaIds {
  id: any;
  isUploaded: boolean;
  type: any;
  mentions?: any[];
}

export interface BlobPayload {
  uri: string;
  type?: string;
  name?: string;
}
export interface ISearchPayload {
  displayName: string;
}
export interface IMedia_CheckIn {
  checkin: boolean;
}

export interface SEARCH_USERS {
  id: number;
  avatarUrl: string;
  displayName: string;
  userName: string;
}

export interface IProfileOptionObject {
  leftImage?: any;
  name: string;
  description?: string;
  rightImage?: any;
  expandedContent?: any;
}

export interface IAboutCardHeader {
  leftImage: any;
  name: string;
  details: any[];
}

export interface IAboutTitleSubtitle {
  title: string;
  subtitle: string;
}

export interface IViolationContent {
  name: string;
  code: string;
  archived: boolean;
}
export interface IViolationResponse {
  categories: {
    policies: IViolationContent[];
    type: string;
  };
}

type PotentialViolation = {
  policy: string;
};

export type IReportContentPayload = {
  id: string;
  type: string;
  potential_violations: PotentialViolation[];
  imprintId?: string;
  userId?: string;
  isGlobal?: boolean;
};
export interface FOLLOWERS {
  fromUser?: any;
  avatarUrl: string;
  displayName: string;
  email: string;
  id: string;
  userName: string;
  blockedImprints: boolean;
  blockedMessages: boolean;
  isUserBlocked: boolean;
}

export type IProfileOptions = {
  id: number;
  name: string;
  serverName?: string;
};

export interface CompleteProfile {
  about: string;
  contact: Contact;
  education: Education[];
  employment: Employment[];
  family: any[];
  overview: Overview;
}

export interface Contact {
  countryCode: string;
  dob: string;
  email: string;
  gender: string;
  phone: string;
}

export interface Education {
  degree: string;
  endDate: string;
  id: string;
  institute: Institute;
  profileId: string;
  startDate: string;
  type: string;
}

export interface Institute {
  domain: string;
  logo: string;
  name: string;
  refId: number;
}

export interface Employment {
  company: Company;
  endDate: string;
  id: string;
  position: string;
  profileId: string;
  startDate: string;
}

export interface SuggestedData {
  domain: string;
  logo: string;
  name: string;
  refId: number;
}

export interface Company {
  domain: string;
  logo: string;
  name: string;
  refId: number;
}

export interface Overview {
  displayName: string;
  userName: string;
  what_i_stand_for: string;
  what_i_value: string;
}

export interface Meta {
  currentPage: number;
  firstPage: number;
  lastPage: number;
  perPage: number;
  total: number;
}

export interface Relation {
  id: string;
  relation: string;
}

export type IMarkers = {
  latitude: number;
  longitude: number;
};

export interface ILocationData {
  date: string;
  latitude: string;
  longitude: string;
  place: string;
}
export interface IRecentChatList {
  isBlocked?: boolean;
  isFlagged?: any[] | null;
  createdAt: string;
  createdBy: string;
  id: string;
  participantList: ParticipantList[];
  unreadMessageCount: number;
  updatedAt: string;
  user: User;
  recentMessage?: recentMessage;
  icon?: string;
  title?: string;
  canMessage: boolean;
}
export interface ParticipantList {
  user: any;
  addedAt: string;
  addedBy: string;
  participant: {
    avatarUrl: string;
    displayName: string;
    email: string;
    id: string;
    userName: string;
  };

  participantID: string;
  removedAt: null;
  removedBy: null;
}
export interface recentMessage {
  content: string;
  conversationId: string;
  createdAt: string;
  deletedBy: [];
  fromUserId: string;
  id: string;
  isDeleted: boolean;
  isRead: boolean;
  readBy: [];
  updatedAt: string;
}

export type ICreateChat = {
  participantList: {
    participantID: string;
    addedBy: string;
  }[];
};

export type IGetMessagesPayload = {
  page: number;
  convId: string;
};

export interface ITimeOption {
  id: string;
  text: string;
  value: string;
  createdAt: string;
  updatedAt: string;
}

export interface ImprintNotification {
  subNotifications: SubNotification[];
  id: string;
  toUserId: string;
  imprintId: string;
  senderType: string;
  category: string;
  action: any;
  isRead: boolean;
  metaId?: string;
  createdAt: string;
  updatedAt: string;
  metaData: any;
}

export interface SubNotification {
  typeTemplate: TypeTemplate;
  user: User;
  createdAt: string;
}

export interface TypeTemplate {
  id: string;
  iconUrl: string;
  body: string;
  type: string;
}
export type IDeleteChat = {
  conversationId?: string;
};

export type IDeleteMessage = {
  msgId?: string | number;
  forAll: boolean;
};

export interface RudiMessageType {
  collectionName: string;
  convId: string;
  fileSummary: string;
  messages: Message[];
  page: number;
  summary: string;
  totalMessageCount: number;
}

export interface Message {
  content: string;
  conversationId: string;
  createdAt: string;
  id: string;
  receiverId?: string;
  senderId?: string;
  senderType: string;
  timestamp: string;
  updatedAt: string;
  news_articles: any[];
}

export interface RudiFileType {
  id: string;
  userId: string;
  fileName: string;
  fileType: string;
  filePath: string;
  fileSummary: string;
  collectionName: string;
  uploadTime: string;
  fileUrl: string;
  message: string;
}

export type EventScrollType = {height: number; width: number};

export type ITagMedia = {
  type: string;
  page: string;
};

export type IRespondTag = {
  imprintId: string;
  approve: boolean;
  notificationId: string;
};

export type IFlagMessagePayload = {
  email: string;
  messageId: string;
  reason: string | null;
};
export type IFlagConversationPayload = {
  conversationID?: string;
  reason: string;
};

export interface INextOfKin {
  createdAt?: string;
  email: string;
  hasApproved?: any;
  id?: string;
  name: string;
  phone?: string;
  relation: string;
  updatedAt?: string;
  userId?: string;
  isEditAble?: boolean;
}

export interface SubscriptionFeatures {
  accessTier: AccessTier[];
  id: string;
  featureName: string;
}

export interface AccessTier {
  tierName: string;
  accessAllowed: boolean;
}

export interface ISubscriptionPackage {
  amount: number;
  currency: string;
  forbiddenFeatures: string[];
  isActive: boolean;
  lookUpKey: string;
  name: string;
  priceId: string;
  productId: string;
}

export interface SubscriptionFeatures {
  accessTier: AccessTier[];
  id: string;
  featureName: string;
}

export interface AccessTier {
  tierName: string;
  accessAllowed: boolean;
}

export interface ISubscriptionPackage {
  amount: number;
  currency: string;
  forbiddenFeatures: string[];
  isActive: boolean;
  lookUpKey: string;
  name: string;
  priceId: string;
  productId: string;
}
export type ISubmitQuizPayload = {
  answers: any[];
  quizId?: string;
};

export type IQuizResponse = {
  id: string;
  questions: I_QUESTION[];
};

export type I_QUESTION = {
  id: string;
  text: string;
  options: Option[];
  questions: any;
};

type Option = {
  id: string;
  text: string;
};

export type IOptions = {id: string; text: string; optionKey?: string};

export interface ISelectedAnswer {
  questionId: string;
  optionId: string;
  optionKey?: string;
}

export type ISuggestedFriendsItem = {
  avatarUrl: string;
  email: string;
  id: string;
  userName: string;
  displayName?: string;
  scores: score;
  isFollower?: boolean;
  followRequestSent?: boolean;
};
export type score = {key: number};

export enum QUIZ_STATUS {
  NOT_ATTEMPTED = 'NOT_ATTEMPTED',
  COMPLETED = 'COMPLETED',
  INPROGRESS = 'INPROGRESS',
}

export enum VISITED_SCREEN {
  NOTIFICATION = 'notification',
}

export type IDeleteImprintPayload = {
  imprintId: string;
};

export interface IProfileModeration {
  profileId: string;
  text: string;
}
export interface IProfileModerationResponse {
  isValidContent: boolean;
}
export interface IModerationResponse {
  isValidContent: boolean;
}
export enum SubscriptionStatus {
  Incomplete = 'incomplete',
  IncompleteExpired = 'incomplete_expired',
  Trialing = 'trialing',
  Active = 'active',
  PastDue = 'past_due',
  Canceled = 'canceled',
  Unpaid = 'unpaid',
  CancellationScheduled = 'cancellation_scheduled',
  Inactive = 'inactive',
  // ✅ New status for cooling-off period
  CoolingOff = 'cooling_off',
}

export interface ISubscriptionResponse {
  charity?: unknown;
  description: unknown;
  price: string;
  subscriptionOfferDetails: any;
  title: unknown;
  id: string; // lookup_key → "promaxplus_plan"
  amount: number; // unit_amount → 0
  currency: string; // currency → "gbp"
  forbiddenFeatures: string[]; // not in original, you decide where it comes from (default: [])
  headerColor: string; // e.g., "#C1900E" (you assign it or derive it)
  isActive: boolean; // active → false
  lookUpKey: string; // lookup_key → "promaxplus_plan"
  name: string; // product.name → "ProMax Plus"
  priceId: string; // id → "price_1OqwWaDGGZUTpTRCpB0zrvdD"
  productId: string;
  product: StripeProduct; // product.id → "prod_PgJ6UNIyCuDM6O"
}
export interface StripeProduct {
  active: boolean;
  attributes: string[];
  created: number;
  default_price: number;
  description: string | null;
  features: any[];
  id: string;
  images: string[];
  livemode: boolean;
  marketing_features: any[];
  metadata: Record<string, any>;
  name: string;
  object: 'product';
  package_dimensions: null;
  shippable: null;
  statement_descriptor: null;
  tax_code: string;
  type: 'service' | string;
  unit_label: string | null;
  updated: number;
  url: string | null;
}

export type IPurchasedPayload = {
  lookupKey: string;
  customerId: string;
  couponId?: string;
};
export type IBillingPayload = {
  customerId: string;
};

export enum ENTERTAINMENTS_ENUMS {
  GOOD_DEEDS = 'GOOD_DEED',
  QUOTES = 'QUOTE',
  JOKES = 'JOKE',
  QUIZ = 'QUIZ',
}

export interface IEntertainmentContentResponse {
  author: string;
  body: string;
  createdAt: string;
  datePublished: string | null;
  id: string;
  imageUrl: string | null;
  message: string | null;
  title: string;
  type: string;
  url: string | null;
  valueType: string;
}
export interface IQuizContentResponse {
  id: string;
  category: string;
  description: {
    about: string;
    asset: string;
    info: string;
    tagline: string;
  };
  reward: {
    id: string;
    title: string;
    description: string;
  };
  status: string;
  title: string;
  totalQuestions: number;
  type: string;
}
export interface IQuiz {
  id: string;
  limit: number;
  page: number;
}

export interface INewsPayload {
  page: number;
  personal: boolean;
  global: boolean;
  filters?: string[];
}
export interface INewsResponse {
  id: string;
  title: string;
  body: string;
  author: string | null;
  imageUrl: string;
  url: string;
  type: string;
  valueType: string;
  message: string;
  datePublished: string;
  userIds: string[];
  createdAt: string;
  updatedAt: string;
}
export interface ITransformedNews {
  datePublished: string;
  id: string;
  imageUrl: string;
  summary: string;
  title: string;
  url: string;
}

export enum OTP_ENUMS {
  USER_VERIFICATION = 'USER-VERIFICATION',
  RESET_PASSWORD = 'RESET-PASSWORD',
}

export enum REPORT_ENUMS {
  CHAT = 'chat',
  PROFILE = 'profile',
  POST = 'post',
  UNDER18 = 'under18',
  ALL = 'all',
  IMPRINT = 'imprint',
  NEGATIVE = 'negative',
  POSITIVE = 'positive',
  MESSAGE = 'Message',
}
export enum ACCOUNT_STATUS {
  SUSPEND = 'SUSPEND',
  BANNED = 'BANNED',
  RESTRICTED = 'RESTRICTED',
}

export enum TABS_ENUMS {
  'ALL' = 'ALL',
  'REQUESTED' = 'REQUESTED',
  'BLOCKED' = 'BLOCKED',
  'SUGGESTED' = 'SUGGESTED',
}
