import _ from 'lodash';
import {connect} from 'react-redux';
import React, {useEffect, useState} from 'react';
import {FlatList, Platform, SafeAreaView} from 'react-native';
import SubscriptionPackage from '../../components/SubscriptionPackage';
import {CustomNavbar} from '../../components';
import {
  getSubscriptionPlanRequest,
  getSubscriptionPackageFeaturesRequest,
} from '../../actions/TimelineActions';
import {SubscriptionFeatures, UserState} from '../../types';
import {useNavigation} from '@react-navigation/native';
import {
  getSubscriptions,
  getProducts,
  Product,
  requestPurchase,
  requestSubscription,
} from 'react-native-iap';

interface ISubscriptionPros {
  getSubscriptionPlanRequest: (callback: (res: any) => void) => void;
  getSubscriptionPackageFeaturesRequest: (callback: (res: any) => void) => void;
  user: UserState;
}

const productSkus = Platform.select({
  android: ['gold_subscription', 'premium_subscription', 'silver_subscription'],
  ios: ['silver_subscription', 'gold_subscription'],
});

export const constants = {
  productSkus,
};

export const APP_SKUS = productSkus;

const InApp: React.FC<ISubscriptionPros> = ({
  getSubscriptionPlanRequest,
  getSubscriptionPackageFeaturesRequest,
  user,
}) => {
  const [subscriptionFeatures, setSubscriptionFeatures] = useState<
    SubscriptionFeatures[]
  >([]);
  const [subscriptionPlan, setSubscriptionPlan] = useState<any>({});
  const [subscriptions, setSubscriptions] = useState<any>([]);
  const navigation = useNavigation();

  useEffect(() => {
    getSubscriptionPlanRequest((res: any) => {
      setSubscriptionPlan(res);
    });
    getSubscriptionPackageFeaturesRequest((res: SubscriptionFeatures[]) => {
      setSubscriptionFeatures(res);
    });
  }, []);

  useEffect(() => {
    console.log('This si app skus', APP_SKUS);

    getSubscriptions({skus: APP_SKUS || []}).then(res => {
      console.log('This is res', res.length);
      setSubscriptions(res);
    });
  }, []);

  const onPlanClicked = (name: string) => {
    const subscription: any = subscriptions.filter((product: Product) =>
      product.title.toLowerCase().includes(name.toLowerCase()),
    );
    console.log('This is sub', subscription);
    handlePurchase(subscription);
  };

  const handlePurchase = async (subscription: any) => {
    try {
      const offerToken =
        subscription[0]?.subscriptionOfferDetails &&
        subscription[0]?.subscriptionOfferDetails[0]?.offerToken;

      const sku = subscription[0]?.productId;
      const productId = subscription[0]?.productId;
      const amount = subscription.price;

      if (Platform.OS === 'android' && !offerToken) {
        console.warn(
          `There are no subscription Offers for selected product (Only required for Google Play purchases): ${productId}`,
        );
      }

      let requestModel: any = {};

      if (Platform.OS === 'ios') {
        requestModel = {
          sku: productId,
          price: amount,
          appAccountToken: '550e8400-e29b-41d4-a716-************',
        };
      } else {
        requestModel = {
          sku: productId,
          ...(offerToken && {
            subscriptionOffers: [{sku: productId, offerToken}],
          }),
          developerPayloadAndroid: user?.userInfo?.email,
          obfuscatedAccountIdAndroid: user?.userInfo?.id,
        };
      }

      const result = await requestSubscription(requestModel);
      console.log('This is result', result);
    } catch (error) {
      console.log('This is error', error);
    }
  };

  function getAmount(inputString: string): string {
    const extractedAmount = inputString.replace(/[^\d.]/g, '');
    return extractedAmount;
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <CustomNavbar
        title={'Subscription'}
        titleSize={16}
        leftBtnPress={() => {
          navigation.goBack();
        }}
        hasBack={true}
      />
      <FlatList
        horizontal={true}
        data={subscriptionPlan}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item}) => (
          <SubscriptionPackage
            features={subscriptionFeatures}
            subscriptionPackage={item}
            onPress={onPlanClicked}
          />
        )}
      />
    </SafeAreaView>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
});

const actions = {
  getSubscriptionPlanRequest,
  getSubscriptionPackageFeaturesRequest,
};

export default connect(mapStateToProps, actions)(InApp);
