import {Dimensions, StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.home,
    flex: 1,
  },

  statusBar: {
    backgroundColor: Colors.white,
    height: getStatusBarHeight() + 30,
  },
  statusBarWithoutNotch: {
    backgroundColor: Colors.white,
    height: 30,
  },
  subHeading: {
    marginHorizontal: Metrics.baseMargin,
    marginTop: Metrics.ratio(10),
  },
  description: {
    marginHorizontal: Metrics.baseMargin,
    marginTop: Metrics.ratio(10),
  },
  hereButton: {
    fontStyle: 'italic',
    color: Colors.black,
    fontSize: Metrics.ratio(14),
  },
  // Content styling
  contentContainer: {
    // Container for main content area
  },
  headingText: {
    marginTop: 20, // Replaces AppStyles.mTop20
  },
  carouselContainer: {
    // Container for carousel component
  },
});
