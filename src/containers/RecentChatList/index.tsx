import React, {useEffect, useRef, useState} from 'react';
import {View} from 'react-native';
import {
  EmptyStateComponent,
  RecentItem,
  CustomNavbar,
  Loader,
  ButtonView,
  Text,
} from '../../components';
import styles from './styles';
import {
  getRecentChatListRequest,
  leaveConversationRequest,
  deleteChatRequest,
} from '../../actions/ChatActions';
import {isRefreshPage} from '../../actions/UserActions';
import {RowMap, SwipeListView} from 'react-native-swipe-list-view';
import CreateChatList from '../CreateChat';

import {
  IBlockEntity,
  IDeleteChat,
  IFlagConversationPayload,
  IRecentChatList,
  RootState,
  UserState,
  generalState,
} from '../../types';
import {connect} from 'react-redux';
import {CHAT_LIST} from '../../constants/StringConstants';
import _ from 'lodash';
import {ADD_BUTTON, RUDI_PROFILE_ICON} from '../../constants/AssetSVGConstants';
import {hasNotch} from 'react-native-device-info';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import SwipeOptionsItem from '../../components/ChatComponents/SwipeOptionsItem';
import MoreOptionsModal from '../../components/ChatComponents/MoreOptionsModal';
import {blockMessageRequest} from '../../actions/FollowersActions';
import {Colors, Fonts, AppStyles} from '../../theme';
import BottomModal from '../../components/ChatComponents/BottomModal';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import Tabs from '../../components/ChatComponents/Tabs';

interface RecentChatListProps {
  deleteChatRequest: (
    payload: IDeleteChat,
    callback: (res: any) => void,
  ) => void;
  leaveConversationRequest: (
    payload: IDeleteChat,
    callback: (res: any) => void,
  ) => void;
  flagConversationRequest: (
    payload: IFlagConversationPayload,
    callback: (res: any) => void,
  ) => void;
  blockMessageRequest: (
    payload: IBlockEntity,
    callback: (res: any) => void,
  ) => void;

  getRecentChatListRequest: (callback: (res: any) => void) => void;
  user: UserState;
  generalReducer: generalState;
  isRefreshPage: Function;
}
const RecentChatList: React.FC<RecentChatListProps> = ({
  getRecentChatListRequest,
  leaveConversationRequest,
  blockMessageRequest,
  deleteChatRequest,
  user,
  generalReducer,
  isRefreshPage,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [othersLoading, setOtherLoading] = useState<boolean>(false);

  const [RecentChatList, setRecentChatList] = useState<IRecentChatList[]>([]);
  const [selectedItem, setSelectedItem] = useState<IRecentChatList>();

  const [visibleModal, setVisibleModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<RowMap<any>>();
  const [singleChatList, setSingleChatList] = useState<boolean>(true);
  const [selectedTab, setSelectedTab] = useState<'single' | 'group'>('single');
  const [allChats, setAllChats] = useState<IRecentChatList[]>([]);

  const bottomModalRef = useRef<BottomSheetModal>(null);
  const FriendsLstRef = useRef<BottomSheetModal>(null);

  const isFocus = useIsFocused();

  const navigation = useNavigation();

  const flatListRef = useRef<any>();

  const pullToRefresh = () => {
    setLoading(true);
    getRecentChatListRequest(res => {
      if (res) {
        setAllChats(res);
        filterChats(res, selectedTab);
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    if (isFocus) {
      fetchData();
    }
  }, [isFocus]);

  useEffect(() => {
    if (generalReducer.isRefreshPage) {
      fetchData();
      isRefreshPage(false);
    }
  }, [generalReducer.isRefreshPage, isRefreshPage]);

  const fetchData = () => {
    setLoading(true);
    getRecentChatListRequest(res => {
      if (res) {
        setAllChats(res);
        filterChats(res, selectedTab);
      }
      setLoading(false);
    });
  };

  const getRecentChatList = () => {
    setLoading(true);
    getRecentChatListRequest(res => {
      if (res) {
        setRecentChatList(res);
        setLoading(false);
      }
      setLoading(false);
    });
  };

  const handleGroupAction = () => {
    setVisibleModal(false);
    const payload: IDeleteChat = {
      conversationId: selectedItem?.id,
    };
    leaveConversationRequest(payload, (res: any) => {
      if (res) {
        setVisibleModal(false);

        setRecentChatList(prevList =>
          prevList.filter(conversation => conversation.id !== selectedItem?.id),
        );
        setAllChats(prevList =>
          prevList.filter(conversation => conversation.id !== selectedItem?.id),
        );
      }
    });
  };

  const onDeleteChat = () => {
    const payload: IDeleteChat = {
      conversationId: selectedItem?.id,
    };
    setTimeout(() => {
      setOtherLoading(true);
      deleteChatRequest(payload, (res: any) => {
        if (res) {
          setRecentChatList(prevList =>
            prevList.filter(
              conversation => conversation.id !== selectedItem?.id,
            ),
          );
          setAllChats(prevList =>
            prevList.filter(
              conversation => conversation.id !== selectedItem?.id,
            ),
          );

          if (selectedRow && selectedItem && selectedRow[selectedItem.id]) {
            selectedRow[selectedItem.id].closeRow();
          }
          setVisibleModal(false);
          setOtherLoading(false);
        }
      });
    }, 500);
    setLoading(false);
    setVisibleModal(false);
  };

  const onMessagePress = () => {
    const payload: IBlockEntity = {
      follower: selectedItem?.participantList.filter(
        parti => parti.participantID !== user.userInfo.userId,
      )[0].participantID,
      status: !selectedItem?.isBlocked ? true : false,
    };
    setTimeout(() => {
      setOtherLoading(true);
      blockMessageRequest(payload, (res: any) => {
        if (res) {
          getRecentChatListRequest(res => {
            if (res) {
              filterChats(res, selectedTab);
              // setRecentChatList(res);
              setLoading(false);
              if (selectedRow && selectedItem && selectedRow[selectedItem.id]) {
                selectedRow[selectedItem.id].closeRow();
              }
            }
            setLoading(false);
          });
          setVisibleModal(false);
          setOtherLoading(false);
        }
      });
    }, 500);
    setLoading(false);
    setVisibleModal(false);
  };

  const renderModal = () => {
    return (
      <MoreOptionsModal
        onDeleteChat={onDeleteChat}
        onBlockChat={onMessagePress}
        selectedItem={selectedItem}
        onLeaveGroupPress={() => handleGroupAction()}
        showMoreModal={visibleModal}
        hideModal={() => setVisibleModal(false)}
      />
    );
  };

  const filterChats = (chats: IRecentChatList[], tab: 'single' | 'group') => {
    const filteredChats = chats.filter(chat =>
      tab === 'single'
        ? chat.participantList.length === 2
        : chat.participantList.length > 2,
    );
    setRecentChatList(filteredChats);
  };

  const renderSwipeOptions = (
    moreItem: IRecentChatList,
    rowMap: RowMap<any>,
  ) => {
    return (
      <SwipeOptionsItem
        item={moreItem}
        onMoreOptionsPress={() => onMoreOptionsPress(moreItem, rowMap)}
      />
    );
  };
  const handleTabChange = (tab: 'single' | 'group') => {
    setSelectedTab(tab);
    filterChats(allChats, tab);
  };

  const renderHeader = () => {
    return (
      <View style={styles.alignSelf}>
        <Tabs
          onChatPress={() => handleTabChange('single')}
          onGroupPress={() => handleTabChange('group')}
          chatList={selectedTab === 'single'}
          singleChatList={allChats.filter(
            item => item.participantList.length === 2,
          )}
          GroupList={allChats.filter(item => item.participantList.length > 2)}
        />
        {selectedTab === 'single' ? (
          <>
            <ButtonView
              style={styles.rudiChatContainer}
              onPress={() =>
                (navigation.navigate as (route: string) => void)(
                  Routes.RUDI_MESSAGE,
                )
              }>
              <RUDI_PROFILE_ICON />
            </ButtonView>
            <Text
              style={[AppStyles.mLeft30, AppStyles.mTop10]}
              textAlign="left"
              color={Colors.black3}
              type="semi_bold"
              size={Fonts.size.buttonText}>
              {CHAT_LIST.RUDI}
            </Text>
          </>
        ) : null}
      </View>
    );
  };

  const onItemPress = (item: IRecentChatList) => {
    const filteredParticipants = item.participantList
      .map(item => item.participant)
      .filter(participant => participant !== undefined);

    bottomModalRef.current?.close();
    setTimeout(() => {
      (navigation.navigate as (route: string, {}) => void)(Routes.CHAT, {
        conversationId: item.id,
        participantList: filteredParticipants,
        isBlocked: item.isBlocked,
        title: item.title,
        canMessage: item.canMessage,
      });
    }, 0);
  };

  const RenderItem = ({item}: {item: IRecentChatList | any}) => {
    return (
      <RecentItem item={item} currentUser={user} onItemPress={onItemPress} />
    );
  };
  const onMoreOptionsPress = (item: IRecentChatList, rowMap: RowMap<any>) => {
    bottomModalRef.current?.close();
    setVisibleModal(true);
    setSelectedItem(item);
    setSelectedRow(rowMap);
  };

  return (
    <View style={styles.container}>
      <View
        style={
          hasNotch() ? styles.statusBar : styles.statusBarWithoutNotch
        }></View>
      <CustomNavbar
        leftBtnPress={() => navigation.goBack()}
        title={CHAT_LIST.INBOX}
        hasMultiRight={true}
        rightBtnImage1={<ADD_BUTTON />}
        rightBtnPress1={() => bottomModalRef.current?.present()}
      />

      <View style={styles.container}>
        <SwipeListView
          ListHeaderComponent={renderHeader}
          ref={flatListRef}
          keyExtractor={item => item.id.toString()}
          closeOnRowOpen
          closeOnRowBeginSwipe
          onRefresh={pullToRefresh}
          refreshing={false}
          data={RecentChatList}
          renderItem={RenderItem}
          renderHiddenItem={(
            {item}: {item: IRecentChatList},
            rowMap: RowMap<any>,
          ) => renderSwipeOptions(item, rowMap)}
          rightOpenValue={-75}
          automaticallyAdjustContentInsets
          recalculateHiddenLayout={true}
          ListEmptyComponent={
            !loading ? (
              <EmptyStateComponent text={CHAT_LIST.EMPTY_CHAT} />
            ) : null
          }
        />
      </View>
      {renderModal()}
      <BottomModal
        onChatPress={() => {
          setSingleChatList(true);

          bottomModalRef.current?.close();
          FriendsLstRef.current?.present();
        }}
        onGroupPress={() => {
          setSingleChatList(false);

          bottomModalRef.current?.close();
          FriendsLstRef.current?.present();
        }}
        bottomSheetModalRef={bottomModalRef}
      />
      <CreateChatList
        bottomSheetModalRef={FriendsLstRef}
        screenTitle={singleChatList ? 'New Chat' : 'New Group'}
        singleChatList={singleChatList}
        selectedTab={item => setSelectedTab(item)}
      />

      <Loader loading={othersLoading} />
    </View>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
  generalReducer: state.generalReducer,
});

const actions = {
  leaveConversationRequest,
  getRecentChatListRequest,
  isRefreshPage,
  blockMessageRequest,
  deleteChatRequest,
};

export default connect(mapStateToProps, actions)(RecentChatList);
