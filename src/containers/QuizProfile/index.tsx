import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Animated,
  Easing,
  ImageBackground,
  SafeAreaView,
} from 'react-native';
import styles from './styles';
import {ITimeOption, Score, UserInfo} from '../../types';
import {AppStyles, Colors, Metrics} from '../../theme';
import {AppButton, CustomNavbar, Text} from '../../components';
import util from '../../util';
import {QUIZ_PROFILE, SIGN_UP} from '../../constants/StringConstants';
import {useNavigation} from '@react-navigation/native';
import Routes from '../../constants/RouteConstants';
import {getValuePortfolioRequest} from '../../actions/AnalyticsActions';
import {connect} from 'react-redux';

interface QuizProfileProps {
  route: {
    params: {
      scores: Score;
      user: UserInfo;
    };
  };
}

interface QuicProfileProps {
  getValuePortfolioRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
}

const QuizProfile: React.FC<QuizProfileProps & QuicProfileProps> = ({
  route,
}) => {
  const AssignStoneValue = util.addScoresToStonesData(route.params.scores);
  const user = route.params.user;
  const navigation = useNavigation();

  const SortedStoneData = AssignStoneValue.sort(
    (a, b) => a.score - b.score,
  ).reverse();

  const animatedValues = useRef(
    SortedStoneData.map(() => new Animated.Value(0)),
  ).current;

  const startAnimation = () => {
    const animations = SortedStoneData.map((_, index) => {
      return Animated.timing(animatedValues[index], {
        toValue: 1,
        duration: 1500,
        easing: Easing.linear,
        useNativeDriver: true,
        delay: 500,
      });
    });
    Animated.sequence(animations).start();
  };

  useEffect(() => {
    setTimeout(() => {
      startAnimation();
    }, 500);
  }, []);

  const [backgroundColors] = useState(
    Array(SortedStoneData.length).fill(Colors.white),
  );

  const [visibleIndexes, setVisibleIndexes] = useState<number[]>([]);

  useEffect(() => {
    SortedStoneData.forEach((_, index) => {
      setTimeout(
        () => {
          setVisibleIndexes(prevIndexes => [...prevIndexes, index]);
        },
        index < 3 ? 3000 * (index + 1) : 2500 * (index + 1),
      );
    });
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <View>
        <CustomNavbar hasBack={true} leftBtnPress={() => navigation.goBack()} />
        <Text
          color={Colors.black}
          size={'medium'}
          textAlign="center"
          style={styles.titleStyle}>
          {QUIZ_PROFILE.TITLE1}
          <Text
            color={Colors.black}
            type="bold"
            size={'medium'}
            textAlign="center">
            {user.displayName}
            {'\n'}
          </Text>
          {QUIZ_PROFILE.TITLE2}
        </Text>
      </View>
      <ImageBackground
        style={styles.cover}
        source={require('../../assets/icons/cover/cover.png')}>
        {SortedStoneData.map((item, index) => {
          const translateY = animatedValues[index].interpolate({
            inputRange: [0, 1],

            outputRange: (() => {
              switch (index) {
                case 0:
                  return [
                    -Metrics.screenHeight * 0.2,
                    Metrics.screenHeight * 0.55 - 100,
                  ];
                case 1:
                  return [
                    -Metrics.screenHeight * 0 - 200,
                    Metrics.screenHeight * 0.55 - 170 - 93,
                  ];
                case 2:
                  return [
                    -Metrics.screenHeight * 0 - 400,
                    Metrics.screenHeight * 0.55 - 170 - 248,
                  ];
                case 3:
                  return [
                    -Metrics.screenHeight * 0 - 600,
                    Metrics.screenHeight * 0.55 - 170 - 170 - 228,
                  ];
                case 4:
                  return [
                    -Metrics.screenHeight * 0 - 800,
                    Metrics.screenHeight * 0.55 - 170 - 170 - 170 - 198,
                  ];
                case 5:
                  return [
                    -Metrics.screenHeight * 0 - 1000,
                    Metrics.screenHeight * 0.55 - 170 - 170 - 170 - 170 - 166,
                  ];

                default:
                  return [0, 0];
              }
            })(),
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={item.id}
              style={[
                styles.animatedView,
                {
                  transform: [{translateY}],
                },
              ]}>
              <View style={styles.imageContainer}>
                <item.image height={100} width={195 - index * 20} />
              </View>
              {visibleIndexes.includes(index as never) && (
                <View
                  id={index.toString()}
                  style={[
                    styles.textContainer,
                    {backgroundColor: backgroundColors[index]},
                  ]}>
                  <Text
                    color={Colors.black}
                    textAlign="center"
                    size={'xxSmall'}
                    style={AppStyles.mLeft5}>
                    <Text
                      color={Colors.black}
                      size={'xxSmall'}
                      textAlign="center">
                      {util.capitalizeFirstLetter(item.text)}:
                    </Text>
                    {item.score}%
                  </Text>
                </View>
              )}
            </Animated.View>
          );
        })}
      </ImageBackground>
      <AppButton
        text={SIGN_UP.NEXT}
        textColor={Colors.white}
        onPress={() =>
          (navigation.navigate as (route: string) => void)(
            Routes.SUGGESTED_FRIENDS,
          )
        }
        buttonStye={styles.doneButton}
      />
    </SafeAreaView>
  );
};

export default connect(null, {
  getValuePortfolioRequest,
})(QuizProfile);
