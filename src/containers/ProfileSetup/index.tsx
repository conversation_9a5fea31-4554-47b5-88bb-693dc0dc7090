import {SafeAreaView, View, Image, Alert, Linking} from 'react-native';
import {SetStateAction, useEffect, useState} from 'react';
import ProgressBar from '../../components/CreateProfile/ProgressBar';
import Title from '../../components/CreateProfile/Title';
import styles from './styles';
import {
  AppButton,
  CustomNavbar,
  Loader,
  Text,
  AgeVerificationModal,
} from '../../components';
import Bio from '../../components/ProfileSetup/Bio';
import UploadImage from '../../components/ProfileSetup/UploadImage';
import UploadedImage from '../../components/ProfileSetup/UploadedImage';
import {PersonalInformation} from '../../components/ProfileSetup/PersonalInformation';
import {useNavigation, useRoute} from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
import {showToastMsg} from '../../components/Alert';
import {
  verifyUserNameRequest,
  setupProfileRequest,
  userInfoRequest,
  userLogout,
  startYotiSessionRequest,
} from '../../actions/UserActions';
import {
  profileModerationRequest,
  profilePhotoModerationRequest,
} from '../../actions/ProfileActions';
import {connect} from 'react-redux';
import {
  IModerationResponse,
  IProfileModeration,
  IVerifyUserNamePayload,
  UserInfo,
  UserState,
} from '../../types';
import BottomSheetModal from '../../components/BottomSheetModal';
import {
  AUTH,
  COMMON,
  PROFILE_SETUP,
  SIGN_UP,
  TOAST_MESSAGES,
} from '../../constants/StringConstants';
import Routes from '../../constants/RouteConstants';
import _ from 'lodash';
import {Colors} from '../../theme';
import util from '../../util';
import PublishButton from '../../components/PublishButton';
import React from 'react';
import YotiService from '../../services/YotiService';

interface ActionsProps {
  userLogout: () => void;
  verifyUserNameRequest: (
    payload: IVerifyUserNamePayload,
    callback: (res: any) => void,
  ) => void;
  userInfoRequest: (callback: (res: any) => void) => void;
  setupProfileRequest: (
    payload: FormData,
    callback: (res: any) => void,
  ) => void;
  startYotiSessionRequest: (callback: (res: any) => void) => void;

  profileModerationRequest: (
    payload: IProfileModeration,
    callback: (res: any) => void,
  ) => void;

  profilePhotoModerationRequest: (
    payload: any,
    callback: (res: any) => void,
  ) => void;
  user: UserState;
}

const ProfileSetup: React.FC<ActionsProps> = ({
  verifyUserNameRequest,
  setupProfileRequest,
  userInfoRequest,
  userLogout,
  profileModerationRequest,
  profilePhotoModerationRequest,
  startYotiSessionRequest,
  user,
}) => {
  const route = useRoute();
  const {personalData} = route.params as {personalData: UserInfo};
  const [checkClicked, setCheckClicked] = React.useState<boolean>(
    personalData.isGoogleLogin ? false : true,
  );

  const [steps, setSteps] = useState(() => {
    if (!personalData) {
      return 0;
    } else if (personalData && personalData.avatarUrl) {
      return 2;
    } else {
      return 0;
    }
  });
  const [image, setImage] = useState<any>(
    personalData ? {path: personalData.avatarUrl} : null,
  );
  const [userName, setUsername] = useState('');
  const [displayName, setDisplayname] = useState(
    personalData ? personalData.displayName : '',
  );

  const [about, setAbout] = useState(personalData ? personalData.bio : '');
  const [correct, setCorrect] = useState(false);
  const navigation = useNavigation();
  const [visibleModal, setVisibleModal] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [showAgeVerificationModal, setShowAgeVerificationModal] =
    useState(false);
  const [dob, setDob] = useState<Date | undefined>(
    personalData ? personalData?.profile?.dob : undefined,
  );
  const [ageModal, setAgeModal] = useState<boolean>(false);
  const [showDobValue, setShowDobValue] = useState<boolean>(false);
  const [phoneValue, setPhoneValue] = useState('');
  const [countryCode, setCountryCode] = useState('US');

  const [valid, setValid] = useState(true);
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [debounceTimers, setDebounceTimers] = useState<{
    displayName: NodeJS.Timeout | null;
    userName: NodeJS.Timeout | null;
    about: NodeJS.Timeout | null;
  }>({
    displayName: null,
    userName: null,
    about: null,
  });

  const [validDisplayName, setValidDisplayName] = useState<boolean>(true);
  const [validAboutContent, setAboutContent] = useState<boolean>(true);

  const [validUserName, setValidUserName] = useState<boolean>(true);
  const DEBOUNCE_DELAY = 500;

  const incrementSteps = async () => {
    if (steps === 2) {
      if (userName === '') {
        showToastMsg(TOAST_MESSAGES.PLEASE_ENTER_USERNAME);
        return;
      }
      if (userName.includes(' ')) {
        showToastMsg(TOAST_MESSAGES.USER_RULES);
        return;
      }
      if (displayName === '') {
        showToastMsg(TOAST_MESSAGES.PLEASE_ENTER_DISPLAY_NAME);
        return;
      }
      if (correct === true) {
        showToastMsg(TOAST_MESSAGES.USER_NAME_ALREADY_EXISTS);
        return;
      }
      if (displayName === '' || displayName === null) {
        showToastMsg(TOAST_MESSAGES.PLEASE_ENTER_DISPLAY_NAME);
        return;
      }
      if (!validUserName) {
        showToastMsg(TOAST_MESSAGES.VALID_USERNAME_ERROR);
        return;
      }
      if (!validDisplayName) {
        showToastMsg(TOAST_MESSAGES.VALID_DISPLAYNAME_ERROR);
        return;
      }

      if (!valid) {
        showToastMsg('Please enter a valid phone number');
        return;
      }

      if (!_.isEmpty(personalData)) {
        if (_.isUndefined(dob)) {
          showToastMsg(TOAST_MESSAGES.PLEASE_SELECT_DOB);
          return;
        }

        if (dob && util.isAgeBetween18(new Date(), dob)) {
          setAgeModal(true);
          return;
        }

        // ✅ Check if age verification is required and not completed
        // if (!isAgeVerified) {
        //   // Try Yoti first, fallback to simple verification
        //   if (validateYotiConfig()) {
        //     console.log('🔧 Using Yoti age verification');
        //     showVerificationModal();
        //   } else {
        //     console.log(
        //       '⚠️ Yoti not configured, using simple age verification',
        //     );
        //     setShowSimpleVerification(true);
        //   }
        //   return;
        // }
      }
      verifyUserNameRequest({userName}, (res: any) => {
        if (res.exists) {
          showToastMsg(TOAST_MESSAGES.USER_NAME_ALREADY_EXISTS);
          setCorrect(true);
        } else {
          setSteps(steps + 1);
        }
      });
    }
    if (steps < 3 && steps !== 2) setSteps(steps + 1);
  };

  const decrementSteps = () => {
    if (steps > 0) setSteps(steps - 1);
  };

  const getStepTtitle = () => {
    switch (steps) {
      case 0:
        return PROFILE_SETUP.UPLOAD_IMAGE;
      case 1:
        return PROFILE_SETUP.PROFILE_IMAGE;
      case 2:
        return PROFILE_SETUP.PERSONAL_INFORMATION;
      case 3:
        return PROFILE_SETUP.BIO;
      default:
        return '';
    }
  };

  const onNextPressed = () => {
    setVisibleModal(false);
    setLoading(true);
    userInfoRequest((res: any) => {
      if (res) {
        setLoading(false);
        (navigation.navigate as (route: string) => void)(Routes.QUIZ_FLOW);
      }
    });
  };

  const onCameraPress = () => {
    openCameraView();
  };

  const onGalleryPress = () => {
    openGalleryView();
  };

  const openGalleryView = async () => {
    ImagePicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
      cropperCircleOverlay: true,
      mediaType: 'photo',
    })
      .then(image => {
        const formData = new FormData();
        const payload = {
          uri: image.path ? image.path : image.sourceURL,
          type: image.mime ? image.mime : 'image/jpeg',
          name: `Image${new Date().getTime()}`,
        };

        formData.append('file', payload);
        setLoading(true);
        profilePhotoModerationRequest(formData, (res: any) => {
          if (res?.isValidContent === true) {
            setLoading(false);

            setImage(image);
            incrementSteps();
          } else {
            setLoading(false);

            showToastMsg(TOAST_MESSAGES.IMAGE_MODERATION_ERROR);
          }
        });
      })
      .catch(error => {
        Alert.alert(
          TOAST_MESSAGES.PERMISSION_ERROR_HEADING,
          TOAST_MESSAGES.ALLOW_PHOTO_PERMISSION,
          [
            {text: 'Open Setting', onPress: () => Linking.openSettings()},
            {text: 'Not Now', onPress: () => {}},
          ],
        );
      });
  };

  const openCameraView = async () => {
    ImagePicker.openCamera({
      cropping: true,
      cropperCircleOverlay: true,
      freeStyleCropEnabled: true,
      mediaType: 'photo',
      compressImageQuality: 1,
    })
      .then(image => {
        const formData = new FormData();
        const payload = {
          uri: image.path ? image.path : image.sourceURL,
          type: image.mime ? image.mime : 'image/jpeg',
          name: `Image${new Date().getTime()}`,
        };
        formData.append('file', payload);
        setLoading(true);
        profilePhotoModerationRequest(formData, (res: any) => {
          if (res?.isValidContent === true) {
            setLoading(false);

            setImage(image);
            incrementSteps();
          } else {
            setLoading(false);

            showToastMsg(TOAST_MESSAGES.IMAGE_MODERATION_ERROR);
          }
        });
        // setImage(image);
        // incrementSteps();
      })
      .catch(error => {
        Alert.alert(
          TOAST_MESSAGES.CAMERA_ERROR_HEADING,
          TOAST_MESSAGES.CAMERA_ERROR_DETAIL,
          [
            {text: 'Open Settings', onPress: () => Linking.openSettings()},
            {text: 'Not Now', onPress: () => {}},
          ],
        );
      });
  };

  const handleUserLogout = () => {
    util.handleUserLogout(userLogout, navigation);
    setShowAgeVerificationModal(false);
  };

  const handleAgeVerificationComplete = () => {
    setShowAgeVerificationModal(false);
    // Show success modal after age verification
    setTimeout(() => {
      setVisibleModal(true);
    }, 1000);
  };

  const onBlurCalled = () => {
    const payload = {
      userName,
    };

    verifyUserNameRequest(payload, (res: any) => {
      if (res.exists) {
        showToastMsg(
          TOAST_MESSAGES.USER_NAME_ALREADY_EXISTS,
          'tomatoToast',
          1000,
          'bottom',
          40,
        );
        setCorrect(true);
      } else {
        setCorrect(false);
      }
    });
  };

  const onFocusCalled = () => {
    setCorrect(false);
  };

  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };

  const onPressRegister = () => {
    console.log('onPressRegister called', checkClicked);

    if (about === '') {
      showToastMsg(TOAST_MESSAGES.PLEASE_ENTER_BIO);
      return;
    }

    if (!validAboutContent) {
      showToastMsg(TOAST_MESSAGES.VALID_ABOUT_CONTENT);
      return;
    }

    if (correct === true) {
      showToastMsg(TOAST_MESSAGES.USER_NAME_ALREADY_EXISTS);
      return;
    }
    if (!checkClicked) {
      showToastMsg(TOAST_MESSAGES.PRIVACY_POLICY_ERROR);
      return;
    }

    setLoading(true);

    const formData = new FormData();
    formData.append('userName', userName);
    formData.append('displayName', displayName);
    formData.append('about', about);
    formData.append('phone', phoneValue);
    formData.append('countryCode', countryCode);
    console.log(formData);

    if (!_.isEmpty(personalData)) {
      formData.append('dob', new Date(dob).toISOString());
    }

    if (!_.isEmpty(image)) {
      const payload = {
        uri: image.path ? image.path : image.sourceURL,
        type: image.mime ? image.mime : 'image/jpeg',
        name: `Image${new Date().getTime()}`,
      };
      formData.append('file', payload);
    }
    setupProfileRequest(formData, (res: any) => {
      setLoading(false);
      if (res) {
        // Check if age verification is needed after profile setup
        userInfoRequest((userRes: any) => {
          if (userRes && YotiService.needsAgeVerification(userRes)) {
            setTimeout(() => {
              setShowAgeVerificationModal(true);
            }, 500);
          } else {
            // No age verification needed, show success modal
            setTimeout(() => {
              setVisibleModal(true);
            }, 1000);
          }
        });
      } else {
        handleUserLogout();
      }
    });
  };

  const setDateOfBirth = (date: SetStateAction<Date | undefined>) => {
    onBlurCalled();
    setShowDobValue(true);
    setDob(date);
  };
  const handleModal = () => {
    setAgeModal(false);
    setSteps(steps + 1);
  };

  const renderAgeModal = () => {
    return (
      <BottomSheetModal
        visible={ageModal}
        onClose={() => setAgeModal(false)}
        onBackdropPress={() => setAgeModal(false)}>
        <View>
          <Text style={styles.title}>{AUTH.AGE_VERIFICATION}</Text>
          <Text style={styles.congratsText}>{AUTH.AGE_VERIFICATION_TEXT}</Text>
          <AppButton
            text={AUTH.DONE}
            textColor={Colors.white}
            onPress={handleModal}
            buttonStye={styles.doneButton}
          />
          <AppButton
            text={AUTH.CANCEL}
            textColor={Colors.black}
            onPress={() => setAgeModal(false)}
            buttonStye={styles.cancelButton}
          />
        </View>
      </BottomSheetModal>
    );
  };
  const renderSteps = () => {
    switch (steps) {
      case 0:
        return (
          <UploadImage
            onCameraPress={onCameraPress}
            onGalleryPress={onGalleryPress}
          />
        );
      case 1:
        return (
          <UploadedImage
            nextButton={incrementSteps}
            retakeButton={decrementSteps}
            imageSource={image.path}
          />
        );
      case 2:
        return (
          <PersonalInformation
            showValue={showDobValue}
            setDob={setDateOfBirth}
            dob={dob}
            personalInformation={personalData}
            nextButton={incrementSteps}
            setDisplayName={setDisplayname}
            setUserName={setUsername}
            userName={userName}
            displayName={displayName}
            onBlur={onBlurCalled}
            onFocus={onFocusCalled}
            correct={correct}
            phoneValue={phoneValue}
            setPhoneValue={setPhoneValue}
            valid={valid}
            setValid={setValid}
            phoneError={phoneError}
            setPhoneError={setPhoneError}
            countryCode={countryCode}
            setCountryCode={setCountryCode}
          />
        );
      case 3:
        return (
          <Bio
            checkClicked={checkClicked}
            onCheckClickedChange={setCheckClicked}
            navigation={navigation}
            isGoogleLogin={personalData.isGoogleLogin}
            onRegisterPressed={onPressRegister}
            setAbout={setAbout}
            about={about}
          />
        );
    }
  };

  const handleDebouncedModeration = (
    key: 'userName' | 'displayName' | 'about',
    text: string,
    setValidState: (value: boolean) => void,
  ) => {
    if (debounceTimers[key]) {
      clearTimeout(debounceTimers[key]!);
    }

    const timer = setTimeout(() => {
      const payLoad: IProfileModeration = {
        profileId: user.userInfo.userId,
        text: text,
      };

      profileModerationRequest(
        payLoad,
        (moderationRes: IModerationResponse) => {
          if (moderationRes) {
            setValidState(moderationRes.isValidContent);
            if (!moderationRes.isValidContent) {
              showToastMsg(`${TOAST_MESSAGES.REVIEW_CONTENT} ${key}`);
            }
          }
        },
      );
    }, DEBOUNCE_DELAY);

    setDebounceTimers(prevTimers => ({
      ...prevTimers,
      [key]: timer,
    }));
  };

  useEffect(() => {
    if (userName) {
      handleDebouncedModeration('userName', userName, setValidUserName);
    }
  }, [userName]);

  useEffect(() => {
    if (displayName) {
      handleDebouncedModeration(
        'displayName',
        displayName,
        setValidDisplayName,
      );
    }
  }, [displayName]);

  useEffect(() => {
    if (displayName) {
      handleDebouncedModeration('about', about, setAboutContent);
    }
  }, [about]);

  // // ✅ Check Yoti verification status on component mount
  // useEffect(() => {
  //   const checkYotiStatus = async () => {
  //     try {
  //       // ✅ Test Yoti configuration on app start
  //       console.log('🔧 Testing Yoti configuration...');
  //       testYotiConfig();

  //       await checkVerificationStatus();
  //     } catch (error) {
  //       console.error('Error checking Yoti verification status:', error);
  //     }
  //   };

  //   checkYotiStatus();
  // }, [checkVerificationStatus]);

  return (
    <SafeAreaView style={styles.container}>
      <CustomNavbar
        title={PROFILE_SETUP.PROFILE_SETUP_TITLE}
        hasBack={steps >= 1 ? true : false}
        leftBtnPress={steps >= 1 ? decrementSteps : () => navigation.goBack()}
        hasMultiRight={true}
        rightBtnImage={
          <PublishButton
            size="xxSmall"
            buttonTitle={COMMON.LOGOUT}
            isPublish={true}
            onPress={handleUserLogout}
          />
        }
      />
      <ProgressBar steps={steps} />
      <Title title={getStepTtitle()} />
      {renderSteps()}
      <BottomSheetModal
        visible={visibleModal}
        onClose={hideModal}
        onBackdropPress={hideModal}>
        <View>
          <Image
            style={styles.gif}
            source={require('../../assets/gif/congrats.gif')}
          />
          <Text style={styles.title}>{SIGN_UP.CONGRATULATIONS}</Text>
          <Text style={styles.congratsText}>{SIGN_UP.CONGRATS_TEXT}</Text>
          <AppButton
            text={SIGN_UP.NEXT}
            textColor={Colors.white}
            onPress={onNextPressed}
            buttonStye={styles.doneButton}
          />
        </View>
      </BottomSheetModal>
      {renderAgeModal()}

      <AgeVerificationModal
        visible={showAgeVerificationModal}
        onVerificationComplete={handleAgeVerificationComplete}
        onStartVerification={startYotiSessionRequest}
        userInfoRequest={userInfoRequest}
        logout={handleUserLogout}
      />

      <Loader loading={loading} />
    </SafeAreaView>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.user,
});

const actions = {
  verifyUserNameRequest,
  profileModerationRequest,
  setupProfileRequest,
  userInfoRequest,
  userLogout,
  profilePhotoModerationRequest,
  startYotiSessionRequest,
};

export default connect(mapStateToProps, actions)(ProfileSetup);
