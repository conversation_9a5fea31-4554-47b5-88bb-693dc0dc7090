// @flow

import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  SafeAreaView,
  Image,
  Platform,
  Text as RNText,
  Alert,
  Linking,
  PermissionsAndroid,
} from 'react-native';
import {
  CustomNavbar,
  Text,
  Avatar,
  AttachmentBottomModal,
  Loader,
  MediaComponent,
  CheckInImage,
  MentionComponent,
} from '../../components';
import styles from './styles';
import {CROSS_ICON} from '../../constants/AssetSVGConstants';
import {
  CREATE_IMPRINT,
  KIN_STATUS,
  TOAST_MESSAGES,
  TOAST_TYPE,
} from '../../constants/StringConstants';
import {AppStyles, Colors, Fonts} from '../../theme';
import PublishButton from '../../components/PublishButton';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {
  useIsFocused,
  useNavigation,
  useNavigationState,
} from '@react-navigation/native';
import MediaModal from '../../components/MediaModal';
import _ from 'lodash';
import PublishModal from '../../components/PublishModal';
import ImagePicker from 'react-native-image-crop-picker';
import {connect} from 'react-redux';
import {
  userPostImprintRequest,
  userUpdateImprintRequest,
} from '../../actions/PostActions';
import {getUserFriendsRequest} from '../../actions/FollowersActions';
import {
  FollowerState,
  IPostImprintPayload,
  IVerifyArticle,
  ImprintCheckIn,
  ImprintMedia,
  MediaItem,
  RootState,
  UserState,
  ImprintMentions,
  SubscriptionStatus,
  UserInfo,
} from '../../types';
import Collage from '../../components/TimeLine/Collage';
import DocumentPicker from 'react-native-document-picker';
import Routes from '../../constants/RouteConstants';
import defaultPdfImage from '../../assets/icons/Pdf.png';
import defaultVideoImage from '../../assets/icons/Video.png';

import {ScrollView} from 'react-native-gesture-handler';
import useMediaUploadService from '../../Hooks/useMediaUpload';
import FriendsModal from '../../components/FreindsModal';
import {showAlertMsg, showToastMsg} from '../../components/Alert';
import NextOfKin from '../../components/NextOfKin';
import {userInfoRequest, getSubscriptionPlans} from '../../actions/UserActions';
import RecordModal from '../../components/CreateImprint/RecordModal';
// import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import AudioPlayer from '../../components/CreateImprint/AudioPlayer';
import AudioRecorderPlayer, {
  AudioSourceAndroidType,
  AVEncodingOption,
  OutputFormatAndroidType,
} from 'react-native-audio-recorder-player';
import Voice from '@react-native-voice/voice';

import {
  IMPRINT_MEDIA_TYPES_MAP,
  TOAST_VISIBILITY_TIMEOUT,
} from '../../constants';
import RNFS from 'react-native-fs';
import util, {
  canCreateCheckin,
  canUploadArticle,
  canUploadImage,
  canUseSpeechToText,
  sanitizeFormFields,
} from '../../util';

import {NativeEventEmitter, NativeModules} from 'react-native';
import {BASE_URL} from '../../config/WebService';

const SpeechToText = NativeModules.SpeechToText;
const eventEmitter =
  Platform.OS == 'android' && new NativeEventEmitter(SpeechToText);
const audioRecorderPlayer = new AudioRecorderPlayer();

interface ActionsProps {
  getSubscriptionPlans: (callback: (res: any) => void) => void;

  userPostImprintRequest: (
    payload: IPostImprintPayload,
    callback: (res: any) => void,
  ) => void;
  userUpdateImprintRequest: (
    payload: any,

    callback: (res: any) => void,
  ) => void;
  getUserFriendsRequest: (callback: (res: any) => void) => void;
  userInfoRequest: (response: (res: any) => void) => void;

  user: UserState;
  subscription: any;

  followers: FollowerState;
  route: {
    params: {
      checkInData: any;
    };
  };
}

const CreateImprint: React.FC<ActionsProps> = ({
  getSubscriptionPlans,

  userPostImprintRequest,
  userUpdateImprintRequest,
  getUserFriendsRequest,
  userInfoRequest,
  user,
  subscription,
  route,
  followers,
}) => {
  const attachmentBottomSheet = useRef<BottomSheetModal>(null);
  const mediaBottomSheet = useRef<BottomSheetModal>(null);
  const RecordBottomSheet = useRef<BottomSheetModal>(null);

  const [visibleModal, setVisibleModal] = useState(false);
  const [personalTribe, setPersonalTribe] = useState(true);
  const [description, setDescription] = useState('');
  const [mediaList, setMediaList] = useState<ImprintMedia[]>([]);
  const [mentionsList, setMentionsList] = useState<ImprintMentions[]>([]);
  const [showMediaList, setShowMediaList] = useState(false);
  const [verify, setVerify] = useState<IPostImprintPayload['verifyArticle']>();
  const [attachments] = useState<ImprintMedia[]>([]);
  const [friendModal, setFriendModal] = useState<boolean>(false);
  const [tagFriends, setTagFriends] = useState<any[]>([]);
  const [showKin, setShowKin] = useState<boolean>(false);

  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [readyForSubmit, setIsReadyForSubmit] = useState(false);
  const [recordedFilePath, setRecordedFilePath] = useState(null);
  const [waveData, setWaveData] = useState(
    Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
  );
  const [isSpeechRecognitionActive, setIsSpeechRecognitionActive] =
    useState(false);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [totalDuration, setTotalDuration] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isRecordingAudio, setIsRecordingAudio] = useState(true);
  const [permissions, setPermissions] = useState<any>({});

  let messageParts: string[] = [];
  let speechEnded = false;
  let accumulatedText = '';

  const isFocused = useIsFocused();

  const [checkIn, setCheckIn] = useState<ImprintCheckIn>({
    place: '',
    lat: '',
    long: '',
    date: new Date(),
    isCheckIn: false,
    checkInImage: '',
  });
  const userName = user.userInfo.userName;

  const navigation = useNavigation();
  const screenStack = useNavigationState(state => state.routes[0].name);

  const {createImprint, loading} = useMediaUploadService(
    userUpdateImprintRequest,
    tagFriends,
  );

  useEffect(() => {
    if (currentPosition >= 20000) {
      onStopRecording();
    }
  }, [currentPosition]);

  React.useEffect(() => {
    if (route.params?.checkInData) {
      setCheckIn({
        checkInImage: route.params.checkInData.checkInImage,
        place: route.params.checkInData.place,
        lat: route.params.checkInData.lat,
        long: route.params.checkInData.long,
        date: new Date(),
        isCheckIn: true,
      });
    }
  }, [route.params?.checkInData]);

  useEffect(() => {
    try {
      const decoded = util.decodeJWT(user.access_token);

      if (decoded) {
        setPermissions(decoded);
      }
    } catch (error) {
      console.error('Invalid token:', error);
    }
  }, [isFocused]);

  const toggleRecordingMode = () => {
    if (isRecordingAudio) {
      onStopRecording();
    } else {
      stopSpeechToText?.();
    }
    setIsRecordingAudio(previousState => !previousState);
  };

  const startSpeechToTextForAndroid = async () => {
    const hasPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    );

    if (!hasPermission) {
      console.error('Audio recording permission is required.');
      return;
    }

    try {
      setIsSpeechRecognitionActive(true);
      setWaveData(prevData => {
        return prevData.map((bar, index) => {
          const randomHeight = Math.random() * 30 + 10; // This would be replaced with `e` data in actual implementation
          return {
            ...bar,
            value: randomHeight, // Use random data for now, map from `e.currentPosition` for actual waveform
            color: '#4CAF50', // Change to green during recording
          };
        });
      });

      Voice.onSpeechPartialResults = r => {
        if (speechEnded) {
          messageParts.push(r.value ? r.value[0] : '');
        }
        accumulatedText =
          messageParts.join(' ') + ' ' + (r.value ? r.value[0] : '');

        setDescription(accumulatedText);
        speechEnded = false;
      };

      Voice.onSpeechResults = res => {
        let speech = res.value?.[0] ?? '';

        if (!speech) {
          speech = messageParts.join(' ');
        }

        setDescription(speech);
        messageParts = [];
      };

      Voice.onSpeechError = error => {
        console.error('Speech Recognition Error:', error);
        setIsSpeechRecognitionActive(false);
      };

      Voice.onSpeechEnd = () => {
        setWaveData(
          Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
        ); // Initialize gray bars
        speechEnded = true;
        Voice.onSpeechResults = res => {
          let speech = res.value?.[0] ?? '';

          if (!speech) {
            speech = messageParts.join(' ');
          }

          setDescription(speech);
          messageParts = [];
        };
      };

      await Voice.start('en-US', {
        EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS: 4000,
      });
    } catch (error) {
      console.error('Error starting speech recognition:', error);
    }
  };

  const stopSpeechToText = () => {
    speechEnded = true;

    Voice.stop();
    Voice.onSpeechResults = res => {
      let speech = res.value?.[0] ?? '';

      if (!speech) {
        speech = messageParts.join(' ');
      }

      setDescription(speech);
      messageParts = [];
    };
  };

  const onStartRecording = async () => {
    if (!isRecordingAudio) {
      startSpeechToTextForAndroid();
    } else {
      const audioSet = {
        AVModeIOS: 'AVAudioSessionModeDefault',
        AVCategoryIOS: 'AVAudioSessionCategoryPlayAndRecord',
        AVCategoryOptionsIOS: 'AVAudioSessionCategoryOptionDefaultToSpeaker',
        AVEncoderAudioQualityKeyIOS: 'AVAudioQualityHigh',
        AVNumberOfChannelsKeyIOS: 2, // 1 for mono, 2 for stereo
        AVSampleRateKeyIOS: 44100, // Sample rate (44.1 kHz)
        AVFormatIDKeyIOS: AVEncodingOption.wav, // Linear PCM encoding
        AVLinearPCMBitDepthKeyIOS: 16, // Bit depth (16-bit)
        AVLinearPCMIsNonInterleaved: false, // Interleaved audio
        AVLinearPCMIsBigEndianKey: false, // Little-endian format
        // Android settings (from the example above)
        outputFormatAndroid: OutputFormatAndroidType.AAC_ADTS,
        // audioEncoderAndroid: 30,
        // audioQualityAndroid: 3,
        // bitRateAndroid: 128000,
        AudioSourceAndroid: AudioSourceAndroidType.MIC,
      };

      // Start the recorder
      const result = await audioRecorderPlayer.startRecorder(
        undefined,
        audioSet,
        true,
      );

      audioRecorderPlayer.addRecordBackListener(e => {
        // Send to Azure for transcription
        setIsRecording(true);
        setCurrentPosition(e.currentPosition);
        setWaveData(prevData => {
          return prevData.map((bar, index) => {
            const randomHeight = Math.random() * 30 + 10; // This would be replaced with `e` data in actual implementation
            return {
              ...bar,
              value: randomHeight, // Use random data for now, map from `e.currentPosition` for actual waveform
              color: '#4CAF50', // Change to green during recording
            };
          });
        });
        if (e.currentPosition >= 5000) {
          onStopRecording();
        }

        return;
      });

      Platform.OS === 'ios' && startSpeechRecognition();
    }
  };

  useEffect(() => {
    return () => {
      Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  const startSpeechRecognition = async () => {
    try {
      setIsSpeechRecognitionActive(true);

      Voice.onSpeechResults = event => {
        const recognizedText = event.value?.[0] || '';
        setDescription(recognizedText);
      };

      Voice.onSpeechError = error => {
        //  destroyRecognizer();
        console.error('Speech Recognition Error:', error);
        setIsSpeechRecognitionActive(false);
      };

      await Voice.start('en-US');
    } catch (error) {
      console.error('Error starting speech recognition:', error);
    }
  };

  const onStopRecording = async () => {
    if (isRecording) {
      try {
        const result = await audioRecorderPlayer.stopRecorder();
        await Voice.stop();
        Voice.destroy().then(Voice.removeAllListeners);
        audioRecorderPlayer.removeRecordBackListener();

        setRecordedFilePath(result);

        // Reset state for wave data and stop timer
        setIsRecording(false);
        setWaveData(
          Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
        ); // Initialize gray bars

        if (timerRef.current) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      } catch (error) {
        console.error('Error stopping recording:', error);
      }
    }
    Platform.OS === 'android' && stopSpeechToText();
  };

  const onStartPlay = async () => {
    const msg = await audioRecorderPlayer.startPlayer(recordedFilePath);
    audioRecorderPlayer.setVolume(1.0);
    setIsPlaying(true);
    audioRecorderPlayer.addPlayBackListener(e => {
      setTotalDuration(e.duration);
      setCurrentPosition(e.currentPosition);
      setIsPlaying(!e.isFinished);
      setWaveData(prevData => {
        return prevData.map((bar, index) => {
          const randomHeight = Math.random() * 30 + 10; // This would be replaced with `e` data in actual implementation
          return {
            ...bar,
            value: randomHeight, // Use random data for now, map from `e.currentPosition` for actual waveform
            color: '#4CAF50', // Change to green during recording
          };
        });
      });
      return;
    });
  };

  const resetState = () => {
    setVisibleModal(false);
    setPersonalTribe(true);
    setDescription('');
    setMediaList([]);
    setMentionsList([]);
    setShowMediaList(false);
    setVerify(undefined);
    setFriendModal(false);
    setTagFriends([]);
    setShowKin(false);
    setCheckIn({
      place: '',
      lat: '',
      long: '',
      date: new Date(),
      isCheckIn: false,
      checkInImage: '',
    });
    setIsRecording(false);
    setIsPlaying(false);
    setRecordedFilePath(null);
    setIsReadyForSubmit(false);
    setWaveData(
      Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
    );
    RecordBottomSheet.current?.close();
    audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.stopPlayer();
    Voice.destroy().then(Voice.removeAllListeners);
    RecordBottomSheet.current?.close();
    mediaBottomSheet.current?.close();
    setCurrentPosition(0);
    attachmentBottomSheet.current?.expand();
  };

  const showNextOfKinPopUP = () => {
    return userInfoRequest((res: UserInfo) => {
      if (res.kinApprovalStatus === KIN_STATUS.PENDING) {
        setShowKin(true);
      }
    });
  };

  useEffect(() => {
    if (isFocused) {
      setTimeout(() => {
        showNextOfKinPopUP();
      }, 100);
    }
  }, [isFocused]);

  useEffect(() => {
    getUserFriendsRequest(() => {});
    attachmentBottomSheet.current?.present();
  }, []);

  useEffect(() => {
    const loadStore = async () => {
      getSubscriptionPlans(res => {});
    };

    loadStore();
  }, []);

  const hideModal = () => {
    setVisibleModal(!visibleModal);
  };

  const handleMediaPress = () => {
    attachmentBottomSheet.current?.snapToIndex(0);
    mediaBottomSheet.current?.present();
  };

  const handleRecordPress = () => {
    attachmentBottomSheet.current?.snapToIndex(0);
    RecordBottomSheet.current?.present();
  };

  const onTagPress = () => {
    attachmentBottomSheet.current?.snapToIndex(0);
    setFriendModal(true);
  };

  const pickDocument = async () => {
    try {
      const res: any[] = await DocumentPicker.pick({
        allowMultiSelection: true,
        copyTo: 'documentDirectory',
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.docx,
          DocumentPicker.types.ppt,
          DocumentPicker.types.pptx,
        ],
      });

      let allAttachments: ImprintMedia[] = [];

      for (let item of res) {
        if (item.size > 5 * 1024 * 1024) {
          Alert.alert(`FILE ${item.name}${TOAST_MESSAGES.DOCUMENT_ERROR} `);
        } else {
          const newAttachment: ImprintMedia = {
            id: Date.now().toString(),
            sourceURL: Image.resolveAssetSource(defaultPdfImage).uri,
            file: Platform.OS === 'android' ? item.fileCopyUri : item.uri,
            mime: item.type,
            filename: `test-${Date.now()}.${item.type.split('/')[1]}`,
          };
          allAttachments.push(newAttachment);
        }
      }

      setMediaList(prevMediaList => [...prevMediaList, ...allAttachments]);
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        throw err;
      }
    }
  };
  const onTakePhoto = () => {
    ImagePicker.openCamera({
      showsSelectedCount: true,
      width: 300,
      compressImageQuality: 0.5,
      height: 400,
      cropping: false,
      cropperCircleOverlay: false,
      multiple: true,
      maxFiles: 10,
      mediaType: 'photo',
    })
      .then((image: any) => {
        setMediaList(image);

        const {mime, path} = image;
        const convertedObject = {
          file: path,
          id: path,
          name: path.substring(path.lastIndexOf('/') + 1),
          sourceURL: path,
          mime: mime,
          filename: path.substring(path.lastIndexOf('/') + 1),
        };
        setMediaList([...mediaList, convertedObject]);
        mediaBottomSheet.current?.close();
        attachmentBottomSheet.current?.present();
      })
      .catch(error => {
        Alert.alert(
          TOAST_MESSAGES.CAMERA_ERROR_HEADING,
          TOAST_MESSAGES.CAMERA_ERROR_DETAIL,
          [
            {text: 'Open Settings', onPress: () => Linking.openSettings()},
            {text: 'Not Now', onPress: () => {}},
          ],
        );
      });
  };

  const onRecordVideo = () => {
    ImagePicker.openCamera({
      showsSelectedCount: true,
      cropping: false,
      cropperCircleOverlay: false,
      compressVideoPreset: 'HighestQuality',
      multiple: true,
      mediaType: 'video',
    })
      .then((image: any) => {
        const {mime, path, duration} = image;

        if (duration && duration > 60000) {
          Alert.alert(TOAST_MESSAGES.RECORD_VIDEO_ERROR);
          return;
        }

        const convertedObject: MediaItem = {
          file: path,
          path: path,
          id: path,
          name: path.substring(path.lastIndexOf('/') + 1),
          sourceURL: Image.resolveAssetSource(defaultVideoImage).uri,
          mime,
          filename: path.substring(path.lastIndexOf('/') + 1),
        };

        const updatedMediaList = [...mediaList, convertedObject];
        setMediaList(updatedMediaList);

        mediaBottomSheet.current?.close();
        attachmentBottomSheet.current?.present();
      })
      .catch(error => {
        Alert.alert(
          TOAST_MESSAGES.CAMERA_ERROR_HEADING,
          TOAST_MESSAGES.CAMERA_ERROR_DETAIL,
          [
            {text: 'Open Settings', onPress: () => Linking.openSettings()},
            {text: 'Not Now', onPress: () => {}},
          ],
        );
      });
  };

  const onGalleryPress = async () => {
    ImagePicker.openPicker({
      // width: 300,
      // height: 400,
      cropping: false,
      multiple: true,
      cropperCircleOverlay: false,
      maxFiles: 10,
      mediaType: 'any',
      compressVideoPreset: 'HighestQuality',
      freeStyleCropEnabled: true,
    })
      .then(items => {
        const modifiedList = items
          .map(item => {
            if (item.size && item.size > 100 * 1024 * 1024) {
              Alert.alert(TOAST_MESSAGES.SELECT_FROM_GALLERY_ERROR);
              return null;
            }

            let modifiedItem = {...item};

            if (modifiedItem.mime === 'video/mp4') {
              modifiedItem.sourceURL =
                Image.resolveAssetSource(defaultVideoImage).uri;
              modifiedItem.file = modifiedItem.path;
            }

            return modifiedItem;
          })
          .filter(item => item !== null) as MediaItem[];

        if (modifiedList.length > 0) {
          const previousMediaList = [...mediaList];
          const updatedMediaList = [
            ...previousMediaList,
            ...modifiedList,
          ] as MediaItem[];
          setMediaList(updatedMediaList);

          mediaBottomSheet.current?.close();
          attachmentBottomSheet.current?.present();
        }
      })
      .catch(error => {
        if (error.message.includes(TOAST_MESSAGES.GALLERY_MSG_ERROR)) {
          Alert.alert(
            TOAST_MESSAGES.PERMISSION_ERROR_HEADING,
            TOAST_MESSAGES.ALLOW_PHOTO_PERMISSION,
            [
              {text: 'Open Settings', onPress: () => Linking.openSettings()},
              {text: 'Not Now', onPress: () => {}},
            ],
          );
        }
      });
  };

  const removeMedia = (index: number) => {
    var newMediaList = [...mediaList];
    if (index > -1) {
      newMediaList.splice(index, 1);
      setMediaList(newMediaList);
    }
    if (mediaList.length == 1) {
      setShowMediaList(false);
    }
  };

  const userInfo = () => {
    const userAvatar = user.userInfo.avatarUrl;
    const getTagText = () => {
      if (!tagFriends || tagFriends.length === 0) {
        return '';
      } else if (tagFriends.length === 1) {
        return ` is with ${tagFriends[0]?.displayName}`;
      } else {
        return ` is with ${tagFriends[0]?.displayName} and ${
          tagFriends.length - 1
        } others`;
      }
    };

    return (
      <View style={styles.userinfoContainer}>
        <Avatar
          image={userAvatar}
          style={styles.imageContainer}
          imageStyle={styles.image}
        />
        <View style={styles.userInfoTextContainer}>
          <Text
            color={Colors.itemColors.titleColor}
            style={AppStyles.mLeft10}
            size={'buttonText'}
            type="bold">
            {userName}

            <RNText
              style={{fontSize: 16, fontFamily: Fonts.type.regular}}
              onPress={() => setFriendModal(true)}>
              {getTagText()}
            </RNText>
          </Text>
        </View>
      </View>
    );
  };

  const checkFileFormat = async filePath => {
    const stats = await RNFS.stat(filePath);
  };

  const submitData = () => {
    const payload: IPostImprintPayload = {
      mediaQuantity: mediaList.length + (recordedFilePath ? 1 : 0),
      isGlobal: !personalTribe,
      isCheckIn: false,
    };
    if (!_.isEmpty(verify)) {
      payload.verifyArticle = verify;
    }
    if (!_.isEmpty(description)) {
      payload.description = sanitizeFormFields(description);
    }

    if (checkIn.isCheckIn) {
      const checkInPayload: ImprintCheckIn = {
        isCheckIn: checkIn.isCheckIn,
        date: checkIn.date,
        place: checkIn.place,
        lat: checkIn.lat.toString(),
        long: checkIn.long.toString(),
      };
      Object.assign(payload, checkInPayload);
    }
    let updatedMediaList = [...mediaList];

    if (recordedFilePath) {
      updatedMediaList.push({
        file: recordedFilePath,
        mime: 'audio/wav',
        filename: `audio_${Date.now()}.wav`,
        name: `audio_${Date.now()}.wav`,
        type: IMPRINT_MEDIA_TYPES_MAP.audio,
      });
    }
    if (!_.isEmpty(mentionsList)) {
      payload.imprintMentions = mentionsList;
    }
    if (!_.isEmpty(tagFriends)) {
      payload.imprintMentions = mentionsList;
    }

    checkFileFormat(checkFileFormat(recordedFilePath));
    userPostImprintRequest(payload, (res: any) => {
      hideModal();

      setTimeout(() => {
        if (res && res.mediaInfo?.length) {
          createImprint(res.mediaInfo, updatedMediaList);
          resetState();
        } else {
          screenStack === Routes.CREATE_IMPRINT
            ? (navigation.navigate as (route: string, params: any) => void)(
                Routes.HOME,
                {
                  screen: Routes.HOME,
                  params: {newPostPublished: true},
                },
              )
            : (navigation.navigate as (route: string) => void)(
                Routes.USER_PROFILE,
              );

          hideModal();
          resetState();
          showToastMsg(TOAST_MESSAGES.POST_SUCCESS, 'success', 5000, 'top');
        }
      }, 500);
    });
  };

  const onVerifyPress = () => {
    const verifyPayload: IVerifyArticle['verifyArticle'] = {
      requestedBy: user.data.userId,
    };

    if (_.isEmpty(verify)) {
      setVerify(verifyPayload);
    } else {
      setVerify(undefined);
    }
  };

  const handleTagFriendList = (selectedTagFriends: any[]) => {
    const transformedItems: any[] = selectedTagFriends.map(item => ({
      userId: item.id,
      userName: item.userName,
      displayName: item.displayName,
      avatarUrl: item.avatarUrl,
      cordx: 0,
      cordy: 0,
    }));
    setTagFriends(transformedItems);
  };

  console.log('tagFriends', permissions);

  const handlePublishPress = () => {
    const imageAndVideoFiles = mediaList.filter(
      item =>
        item.mime &&
        (item.mime.startsWith('image/') || item.mime.startsWith('video/')),
    );
    if (imageAndVideoFiles.length > 10) {
      return showToastMsg(TOAST_MESSAGES.MEDIA_COUNT_ERROR);
    }
    if (
      user.userInfo.subscriptionStatus === SubscriptionStatus.INCOMPLETE ||
      user.userInfo.subscriptionStatus === SubscriptionStatus.EXPIRED
    ) {
      return showAlertMsg(CREATE_IMPRINT.SUBSCRIPTION_ERROR, '', [
        {
          text: 'Yes',
          onPress: () => Linking.openURL(BASE_URL.replace('/backend', '') + ''),
          style: 'default',
        },
        {
          text: 'No',
          onPress: () => {},
          style: 'cancel',
        },
      ]);
      // return Alert.alert(CREATE_IMPRINT.SUBSCRIPTION_ERROR);
    }
    if (user.userInfo.kinApprovalStatus === KIN_STATUS.PENDING) {
      showNextOfKinPopUP();
      return;
    }
    return setVisibleModal(true);
  };

  const resetRecordingState = () => {
    audioRecorderPlayer.stopPlayer();
    setIsRecording(false);
    setIsPlaying(false);
    setIsReadyForSubmit(false);
    setRecordedFilePath(null);
    setWaveData(
      Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
    );
    setCurrentPosition(0);
  };
  const onStopPlay = () => {
    setIsPlaying(false);
    audioRecorderPlayer.stopPlayer();
    setWaveData(
      Array(200).fill({value: Math.random() * 40 + 10, color: '#C4C4C4'}),
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {showKin ? (
        <View style={styles.kinContainer}>
          <NextOfKin
            crossClicked={() => setShowKin(false)}
            ownEmail={user.userInfo.email}
          />
        </View>
      ) : (
        <></>
      )}
      <CustomNavbar
        titleContainer={AppStyles.mRight30}
        leftBtnPress={() => {
          resetState();
          navigation.goBack();
        }}
        leftBtnImage={<CROSS_ICON />}
        title={CREATE_IMPRINT.NAVBAR_TITLE}
        hasRight
        rightBtnImage={
          <PublishButton
            isPublish={
              readyForSubmit ||
              !_.isEmpty(description) ||
              !_.isEmpty(mediaList) ||
              !_.isEmpty(checkIn.checkInImage)
                ? true
                : false
            }
            onPress={handlePublishPress}
          />
        }
      />
      {showMediaList && (
        <MediaComponent
          mediaList={mediaList}
          removeMedia={index => removeMedia(index)}
          onRightPress={() => setShowMediaList(false)}
        />
      )}

      {userInfo()}
      <ScrollView
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps={true}
        showsHorizontalScrollIndicator={false}>
        <MentionComponent
          postDescription={description}
          followersList={followers.data}
          onChangePostDescription={value => setDescription(value)}
          mentionsList={mentionsList}
          setMentionsList={setMentionsList}
        />
        {readyForSubmit && (
          <AudioPlayer
            extraStyle={{width: '70%'}}
            onStartPlay={onStartPlay}
            waveData={waveData}
            DeleteAudio={() => {
              setIsReadyForSubmit(false);
              audioRecorderPlayer.stopPlayer();
              setRecordedFilePath(null);
              setWaveData(
                Array(200).fill({
                  value: Math.random() * 40 + 10,
                  color: '#C4C4C4',
                }),
              );
              setIsRecording(false);
              setCurrentPosition(0);
            }}
            totalDuration={totalDuration}
            currentPosition={currentPosition}
            onStopPlay={onStopPlay}
            isPlying={isPlaying}
          />
        )}

        {!_.isEmpty(checkIn.checkInImage) && (
          <CheckInImage
            imageUri={checkIn.checkInImage}
            removeImage={() =>
              setCheckIn({
                place: '',
                lat: '',
                long: '',
                date: new Date(),
                isCheckIn: false,
                checkInImage: '',
              })
            }
          />
        )}
        {!_.isEmpty(mediaList) && (
          <View style={styles.collagePadding}>
            <Collage
              data={mediaList}
              handleMediaClicked={() => setShowMediaList(true)}
            />
          </View>
        )}
      </ScrollView>
      <AttachmentBottomModal
        onMediaPress={() => {
          console.log('canUploadImage', permissions);

          if (!canUploadImage(permissions)) {
            showToastMsg(
              TOAST_MESSAGES.SUBSCRIPTION_ERROR,
              TOAST_TYPE.DEFAULT,
              TOAST_VISIBILITY_TIMEOUT,
              'top',
            );
            return;
          }
          handleMediaPress();
        }}
        onTagPress={() => onTagPress()}
        onArticlesPress={() => {
          if (!canUploadArticle(permissions)) {
            showToastMsg(
              TOAST_MESSAGES.SUBSCRIPTION_ERROR,
              TOAST_TYPE.DEFAULT,
              TOAST_VISIBILITY_TIMEOUT,
              'top',
            );
            return;
          }
          pickDocument();
        }}
        onCheckInPress={() => {
          if (!canCreateCheckin(permissions)) {
            showToastMsg(
              TOAST_MESSAGES.SUBSCRIPTION_ERROR,
              TOAST_TYPE.DEFAULT,
              TOAST_VISIBILITY_TIMEOUT,
              'top',
            );
            return;
          }
          (
            navigation.navigate as (
              route: string,
              param: {userName: string},
            ) => void
          )(Routes.CHECK_IN, {userName: userName});
        }}
        onVerifyPress={onVerifyPress}
        verify={_.isEmpty(verify)}
        bottomSheetModalRef={attachmentBottomSheet}
        onRecordPress={() => {
          if (!canUseSpeechToText(permissions)) {
            showToastMsg(
              TOAST_MESSAGES.SUBSCRIPTION_ERROR,
              TOAST_TYPE.DEFAULT,
              TOAST_VISIBILITY_TIMEOUT,
              'top',
            );
            return;
          }
          handleRecordPress();
        }}
      />
      <MediaModal
        onLibraryPress={() => onGalleryPress()}
        onPhotoPress={() => onTakePhoto()}
        bottomSheetModalRef={mediaBottomSheet}
        onRecordVideo={() => onRecordVideo()}
      />
      <RecordModal
        onStartRecording={!isRecording ? onStartRecording : onStopRecording}
        onStopRecording={() => onStopRecording()}
        bottomSheetModalRef={RecordBottomSheet}
        onStartPlay={() => onStartPlay()}
        waveData={waveData}
        recordedFilePath={recordedFilePath}
        resetRecording={() => resetRecordingState()}
        isReadyForSubmit={readyForSubmit}
        submitRecording={() => {
          setIsReadyForSubmit(true);
          RecordBottomSheet.current?.close();
        }}
        isRecording={isRecording}
        isPlaying={isPlaying}
        timers={Math.floor(currentPosition / 1000)}
        isRecordingAudio={isRecordingAudio}
        toggleRecordingMode={toggleRecordingMode}
        onStopPlay={onStopPlay}
      />
      <PublishModal
        publishModal={visibleModal}
        toggleGlobalButton={() => setPersonalTribe(false)}
        togglePersonalButton={() => setPersonalTribe(true)}
        onCancelPress={() => hideModal()}
        onDonePressed={() => submitData()}
        hideModal={hideModal}
        personalTribe={personalTribe}
      />
      <FriendsModal
        friendsData={followers.data}
        visible={friendModal}
        hideModal={() => setFriendModal(false)}
        onSubmit={tagFriendsList => handleTagFriendList(tagFriendsList)}
      />
      <Loader loading={loading} />
    </SafeAreaView>
  );
};

const mapStateToProps = (state: RootState) => ({
  user: state.user,
  subscription: state.subscription.data,
  followers: state.followers,
});

const actions = {
  getSubscriptionPlans,
  getUserFriendsRequest,
  userPostImprintRequest,
  userUpdateImprintRequest,
  userInfoRequest,
};

export default connect(mapStateToProps, actions)(CreateImprint);
