# Yoti Integration Example

## 📱 **How to Integrate into ProfileSetup**

Here's how to add the Yoti age verification to your existing ProfileSetup component:

### **1. Import the Component**
```typescript
// In src/containers/ProfileSetup/index.tsx
import {YotiAgeVerification} from '../../components';
```

### **2. Add State Management**
```typescript
const [showYotiVerification, setShowYotiVerification] = useState(false);
const [ageVerified, setAgeVerified] = useState(false);
```

### **3. Update the Next Button Logic**
```typescript
const nextButton = () => {
  if (currentStep === 2) {
    // Personal Information step
    if (!_.isEmpty(personalData)) {
      if (_.isUndefined(dob)) {
        showToastMsg(TOAST_MESSAGES.PLEASE_SELECT_DOB);
        return;
      }

      if (dob && util.isAgeBetween18(new Date(), dob)) {
        setAgeModal(true);
        return;
      }

      // ✅ NEW: Check if age verification is required
      if (!ageVerified) {
        setShowYotiVerification(true);
        return;
      }
    }
  }

  // Continue with existing logic...
  setCurrentStep(currentStep + 1);
};
```

### **4. Add the Yoti Component to Render**
```typescript
return (
  <SafeAreaView style={styles.container}>
    {/* Existing ProfileSetup content */}
    
    {/* ✅ NEW: Yoti Age Verification Modal */}
    <YotiAgeVerification
      visible={showYotiVerification}
      onClose={() => setShowYotiVerification(false)}
      onSuccess={() => {
        setAgeVerified(true);
        setShowYotiVerification(false);
        // Proceed to next step
        setCurrentStep(currentStep + 1);
      }}
      onError={(error) => {
        console.error('Yoti verification error:', error);
        Alert.alert('Verification Error', error);
        setShowYotiVerification(false);
      }}
      userId={user?.userInfo?.userId || ''}
      minimumAge={18}
    />
  </SafeAreaView>
);
```

## 🔧 **Backend API Endpoints**

You'll need to implement these endpoints in your backend:

### **1. Create Session**
```
POST /api/yoti/create-session
Content-Type: application/json

{
  "userId": "user123",
  "minimumAge": 18,
  "platform": "ios"
}

Response:
{
  "success": true,
  "sessionId": "session_abc123",
  "sessionUrl": "https://api.yoti.com/idv/v1/web/sessions/session_abc123",
  "qrCodeUrl": "https://yourapp.com/yoti-qr/session_abc123"
}
```

### **2. Check Status**
```
GET /api/yoti/session-status/session_abc123

Response:
{
  "status": "COMPLETED",
  "result": {
    "ageVerified": true,
    "userAge": 25
  }
}
```

## 🎯 **User Experience Flow**

1. **User enters DOB** in ProfileSetup Step 2
2. **Clicks Next** → System validates age (18+)
3. **Yoti modal appears** with two options:
   - **QR Code**: Opens QR code in browser
   - **Direct Link**: Opens verification page in browser
4. **User completes verification** in browser/Yoti app
5. **App polls for completion** every 3 seconds
6. **On success**: Modal closes, proceeds to Step 3
7. **On error**: Shows error message, allows retry

## ✅ **Benefits of This Approach**

1. **No WebView Issues**: Uses external browser
2. **Mobile-Friendly**: QR code scanning works perfectly
3. **Reliable**: Backend polling ensures status updates
4. **User Choice**: Multiple verification methods
5. **Error Handling**: Comprehensive error management
6. **Secure**: Official Yoti verification flow

## 🔧 **Configuration Options**

### **Customize Minimum Age**
```typescript
<YotiAgeVerification
  minimumAge={21} // Change from 18 to 21
  // ... other props
/>
```

### **Custom Error Handling**
```typescript
<YotiAgeVerification
  onError={(error) => {
    // Custom error handling
    if (error.includes('age')) {
      Alert.alert('Age Verification Failed', 'You must be 18 or older to continue.');
    } else {
      Alert.alert('Verification Error', 'Please try again or contact support.');
    }
  }}
  // ... other props
/>
```

### **Persistence (Optional)**
```typescript
// Store verification status in AsyncStorage
const storeAgeVerification = async () => {
  try {
    await AsyncStorage.setItem('age_verified', 'true');
    await AsyncStorage.setItem('age_verified_date', new Date().toISOString());
  } catch (error) {
    console.error('Failed to store age verification:', error);
  }
};

// Check stored verification
const checkStoredVerification = async () => {
  try {
    const verified = await AsyncStorage.getItem('age_verified');
    const verifiedDate = await AsyncStorage.getItem('age_verified_date');
    
    if (verified === 'true' && verifiedDate) {
      const daysSince = (Date.now() - new Date(verifiedDate).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSince < 30) { // Valid for 30 days
        setAgeVerified(true);
      }
    }
  } catch (error) {
    console.error('Failed to check stored verification:', error);
  }
};
```

This solution provides a robust, mobile-friendly alternative to WebView integration while maintaining security and user experience.
