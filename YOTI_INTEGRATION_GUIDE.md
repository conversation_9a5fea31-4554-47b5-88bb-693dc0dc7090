# Yoti Age Verification Integration Guide - Backend Approach

## 🎯 **Proper Yoti Integration Complete!**

I've completely reverted to the correct Yoti Identity Verification approach using backend services as per the official Yoti documentation. Here's what has been implemented:

## 📱 **New User Flow:**

### **Email Signup Flow:**

1. **Email Signup** → DOB collected → **Profile Setup Step 2** → **Yoti Age Verification** → **Step 3 (Bio)** → Complete

### **Social Signup Flow:**

1. **Social Signup** → **Profile Setup Step 2** → DOB collected → **Yoti Age Verification** → **Step 3 (Bio)** → Complete

## ✅ **What's Been Implemented:**

### **1. Yoti Age Verification Component** (`src/components/YotiAgeVerification/`)

- **WebView-based** Yoti integration
- **Secure age verification** using Yoti Web SDK
- **Error handling** and retry functionality
- **Mobile-optimized** UI with loading states

### **2. Yoti Hook** (`src/hooks/useYotiAgeVerification.ts`)

- **State management** for verification status
- **AsyncStorage persistence** (30-day expiry)
- **Automatic status checking**
- **Error handling** and retry logic

### **3. Yoti Configuration** (`src/config/yotiConfig.ts`)

- **Environment-specific** settings (dev/prod)
- **Configurable minimum age** (default: 18)
- **Feature toggles** for different Yoti services
- **Error message constants**

### **4. Profile Setup Integration**

- **Modified ProfileSetup** to include Yoti verification
- **Smart flow control** - only shows Yoti if age verification needed
- **Seamless integration** between Step 2 and Step 3

## 🔧 **Setup Required:**

### **1. Get Yoti Credentials**

1. **Sign up** at [Yoti Hub](https://hub.yoti.com/)
2. **Create an application** for age verification
3. **Get your credentials**:
   - Client SDK ID
   - Scenario ID

### **2. Configure Yoti Settings**

Update `src/config/yotiConfig.ts`:

```typescript
export const YOTI_CONFIG = {
  development: {
    clientSdkId: 'YOUR_DEV_CLIENT_SDK_ID', // Replace with your dev credentials
    scenarioId: 'YOUR_DEV_SCENARIO_ID', // Replace with your dev scenario
    minimumAge: 18,
    environment: 'sandbox',
  },
  production: {
    clientSdkId: 'YOUR_PROD_CLIENT_SDK_ID', // Replace with your prod credentials
    scenarioId: 'YOUR_PROD_SCENARIO_ID', // Replace with your prod scenario
    minimumAge: 18,
    environment: 'production',
  },
};
```

### **3. Test the Integration**

1. **Run the app** in development mode
2. **Go through signup** flow
3. **Reach Profile Setup Step 2**
4. **Enter DOB** (18+ years)
5. **Click Next** → Yoti verification should appear
6. **Complete verification** → Should proceed to Step 3

## 🎯 **How It Works:**

### **Age Verification Logic:**

```typescript
// 1. User enters DOB in Profile Setup Step 2
// 2. System checks if user is under 18 using DOB
if (dob && util.isAgeBetween18(new Date(), dob)) {
  setAgeModal(true); // Show under-18 modal
  return;
}

// 3. If 18+, check if Yoti verification is completed
if (!isAgeVerified) {
  showVerificationModal(); // Show Yoti verification
  return;
}

// 4. If verified, proceed to Step 3
```

### **Verification Persistence:**

- **Verification stored** in AsyncStorage for 30 days
- **Automatic expiry** - user needs to re-verify after 30 days
- **Cross-session persistence** - verification survives app restarts

### **Error Handling:**

- **Configuration validation** before showing Yoti
- **Network error handling** with retry options
- **User-friendly error messages**
- **Graceful fallbacks** if Yoti is unavailable

## 🔍 **Testing Scenarios:**

### **1. First-Time User (No Verification)**

- **Expected**: Yoti modal appears after DOB entry
- **Action**: Complete Yoti verification
- **Result**: Proceeds to Bio step

### **2. Returning User (Already Verified)**

- **Expected**: No Yoti modal, direct to Bio step
- **Action**: None needed
- **Result**: Smooth flow to Bio step

### **3. Verification Expired (30+ days)**

- **Expected**: Yoti modal appears again
- **Action**: Re-verify with Yoti
- **Result**: New 30-day verification period

### **4. Under 18 User**

- **Expected**: Age restriction modal (existing behavior)
- **Action**: User cannot proceed
- **Result**: Blocked from continuing

### **5. Configuration Error**

- **Expected**: Configuration error alert
- **Action**: Contact support message
- **Result**: User cannot proceed until fixed

## 🚀 **Benefits:**

1. **Regulatory Compliance** - Meets age verification requirements
2. **Privacy-Focused** - Yoti doesn't store personal data
3. **User-Friendly** - Seamless integration into existing flow
4. **Secure** - Industry-standard verification process
5. **Flexible** - Easy to configure and customize

## 📋 **Next Steps:**

1. **Get Yoti credentials** from Yoti Hub
2. **Update configuration** with your credentials
3. **Test the flow** with different scenarios
4. **Deploy to production** when ready

## 🔧 **Customization Options:**

### **Change Minimum Age:**

```typescript
// In yotiConfig.ts
minimumAge: 21, // Change from 18 to 21
```

### **Adjust Verification Expiry:**

```typescript
// In useYotiAgeVerification.ts
const VERIFICATION_EXPIRY_DAYS = 90; // Change from 30 to 90 days
```

### **Add Additional Verification:**

```typescript
// In yotiConfig.ts
identityVerification: {
  enabled: true,
  requiredAttributes: ['given_names', 'family_name', 'nationality'],
},
```

The integration is complete and ready for testing! 🎉
