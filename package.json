{"name": "imPrintAI", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@gorhom/bottom-sheet": "^4", "@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-async-storage/async-storage": "^1.22.3", "@react-native-community/geolocation": "^3.2.0", "@react-native-community/netinfo": "^11.3.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "^6.5.18", "@react-navigation/native": "^6.1.15", "@react-navigation/native-stack": "^6.9.18", "@shopify/flash-list": "^1.6.4", "@types/react-native-video": "^5.0.20", "add": "^2.0.6", "apisauce": "^3.0.1", "axios": "^1.6.7", "base-64": "^1.0.0", "deprecated-react-native-prop-types": "^5.0.0", "expo-modules-core": "^1.12.23", "html-entities": "^2.5.2", "libphonenumber-js": "^1.11.2", "moment": "^2.30.1", "password-validator": "^5.3.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-native": "0.73.4", "react-native-audio-recorder-player": "^3.6.12", "react-native-avoid-softinput": "^7.0.0", "react-native-blob-util": "^0.19.8", "react-native-chart-kit": "^6.12.0", "react-native-circular-progress": "^1.4.1", "react-native-controlled-mentions": "^2.2.5", "react-native-date-picker": "^4.4.0", "react-native-device-info": "^10.13.1", "react-native-document-picker": "^9.3.0", "react-native-dropdown-picker": "^5.4.6", "react-native-fast-image": "^8.6.3", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.21.2", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.8.1", "react-native-google-places-autocomplete": "2.5.6", "react-native-iap": "^12.16.2", "react-native-image-crop-picker": "^0.40.3", "react-native-image-placeholder": "^1.0.14", "react-native-iphone-x-helper": "^1.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.16.8", "react-native-keyboard-manager": "^6.5.11-2", "react-native-map-clustering": "^3.4.2", "react-native-maps": "1.15.3", "react-native-message-bar": "^2.1.0", "react-native-modal": "^13.0.1", "react-native-otp-entry": "^1.8.2", "react-native-outside-press": "^1.2.2", "react-native-pdf": "^6.7.4", "react-native-phone-number-input": "^2.1.0", "react-native-progress": "^5.0.1", "react-native-reanimated": "3.8.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "3.29.0", "react-native-share": "^10.2.1", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.0.0", "react-native-svg-transformer": "^1.3.0", "react-native-swipe-list-view": "^3.2.9", "react-native-swiper": "^1.6.0", "react-native-toast-message": "^2.2.0", "react-native-video": "^5.2.1", "react-native-view-shot": "^3.8.0", "react-native-webview": "^13.10.3", "react-redux": "^9.1.0", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "redux-storage": "^4.1.2", "redux-storage-decorator-filter": "^1.1.8", "redux-storage-engine-localstorage": "^1.1.4", "remote-redux-devtools": "^0.5.16", "sanitize-html": "^2.14.0", "seamless-immutable": "^7.1.4", "uuid": "^10.0.0", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/lodash": "^4.14.202", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@types/redux-logger": "^3.0.13", "@types/redux-storage": "^4.1.3", "@types/remote-redux-devtools": "^0.5.8", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}