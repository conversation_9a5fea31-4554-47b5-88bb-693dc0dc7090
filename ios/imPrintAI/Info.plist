<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Imprint</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.854449431593-i1et3hhisopnfpdc9ftonmdjmtktgjph</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	
	<key>NSCameraUsageDescription</key>
	<string>for the use of profile picture</string>
	<key>NSMicrophoneUsageDescription</key>
<string>Give $(PRODUCT_NAME) permission to use your microphone. Your record wont be shared without your permission.</string>
 <key>NSSpeechRecognitionUsageDescription</key>
  <string>Description of why you require the use of the speech recognition</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>
	To enable check-ins, access to your location is required. Your privacy is our priority
	</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To enable check-ins, access to your location is required. Your privacy is our priority. </string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Need microphone access for uploading audio</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>for the use of profile picture</string>
	<key>UIAppFonts</key>
	<array>
		<string>Poppins-Regular.ttf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-Bold.ttf</string>
		<string>Poppins-ExtraBold.ttf</string>
		<string>Poppins-SemiBold.ttf</string>
		<string>Poppins-Thin.ttf</string>
		<string>Poppins-Black.ttf</string>
		<string>Poppins-BlackItalic.ttf</string>
		<string>Poppins-BoldItalic.ttf</string>
		<string>Poppins-ExtraBoldItalic.ttf</string>
		<string>Poppins-ExtraLight.ttf</string>
		<string>Poppins-ExtraLightItalic.ttf</string>
		<string>Poppins-Italic.ttf</string>
		<string>Poppins-Light.ttf</string>
		<string>Poppins-LightItalic.ttf</string>
		<string>Poppins-MediumItalic.ttf</string>
		<string>Poppins-SemiBoldItalic.ttf</string>
		<string>Poppins-ThinItalic.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
