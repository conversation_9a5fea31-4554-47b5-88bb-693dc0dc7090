# Yoti Mobile Integration Solution

## 🎯 **Problem Solved**

Your WebView integration wasn't working on mobile, but QR code scanning works on web. This solution provides a **backend-based approach** that works reliably on mobile devices.

## 📱 **How It Works**

### **User Flow:**
1. User enters DOB in profile setup
2. If 18+, Yoti verification modal appears
3. User chooses verification method:
   - **QR Code**: Opens QR code in browser, user scans with Yoti app
   - **Direct Link**: Opens verification page in browser
4. App polls backend for completion status
5. Once verified, user proceeds to next step

### **Technical Flow:**
1. **Frontend** calls `/api/yoti/create-session`
2. **Backend** creates Yoti session and returns URLs
3. **Frontend** opens URL in external browser (not WebView)
4. **User** completes verification in browser/Yoti app
5. **Frontend** polls `/api/yoti/session-status/{sessionId}`
6. **Backend** returns completion status
7. **Frontend** proceeds based on result

## 🔧 **Backend Implementation Required**

### **1. Install Yoti SDK**
```bash
# Node.js
npm install @yoti/identity-verification

# Python  
pip install yoti-python-sdk
```

### **2. Create Session Endpoint**
```javascript
// POST /api/yoti/create-session
app.post('/api/yoti/create-session', async (req, res) => {
  try {
    const { userId, minimumAge, platform } = req.body;
    
    const sessionConfig = {
      user_tracking_id: userId,
      notifications: {
        endpoint: `${process.env.BASE_URL}/api/yoti/webhook`,
        topics: ['session_completion']
      },
      requested_checks: [{
        type: 'ID_DOCUMENT_AUTHENTICITY',
        config: {
          manual_check: 'NEVER'
        }
      }],
      requested_tasks: [{
        type: 'ID_DOCUMENT_TEXT_DATA_EXTRACTION',
        config: {
          manual_check: 'NEVER',
          chip_data: 'DESIRED'
        }
      }],
      sdk_config: {
        allowed_capture_methods: 'CAMERA_AND_UPLOAD',
        primary_colour: '#2d9cdb',
        secondary_colour: '#ffffff',
        font_colour: '#000000',
        locale: 'en-GB',
        preset_issuing_country: 'GBR'
      }
    };

    const session = await yotiClient.createSession(sessionConfig);
    
    res.json({
      success: true,
      sessionId: session.session_id,
      sessionUrl: session.client_session_token_ttl,
      qrCodeUrl: `${process.env.FRONTEND_URL}/yoti-qr/${session.session_id}`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});
```

### **3. Status Check Endpoint**
```javascript
// GET /api/yoti/session-status/:sessionId
app.get('/api/yoti/session-status/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const session = await yotiClient.getSession(sessionId);
    
    let result = null;
    if (session.state === 'COMPLETED') {
      const checks = session.checks || [];
      const idCheck = checks.find(check => 
        check.type === 'ID_DOCUMENT_AUTHENTICITY'
      );
      
      result = {
        ageVerified: idCheck?.result === 'PASS',
        userAge: extractAgeFromSession(session)
      };
    }
    
    res.json({
      status: session.state,
      result: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});
```

### **4. QR Code Page (Optional)**
Create a simple web page that displays the QR code:

```html
<!-- /yoti-qr/:sessionId -->
<!DOCTYPE html>
<html>
<head>
    <title>Yoti Age Verification</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <div style="text-align: center; padding: 20px;">
        <h2>Age Verification</h2>
        <p>Scan this QR code with your Yoti app:</p>
        <div id="qr-code"></div>
        <p><small>Or use the Yoti app to scan this code</small></p>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        const sessionId = window.location.pathname.split('/').pop();
        const qrData = `yoti://verify/${sessionId}`;
        
        QRCode.toCanvas(document.getElementById('qr-code'), qrData, {
            width: 256,
            margin: 2
        });
    </script>
</body>
</html>
```

## 🚀 **Frontend Integration**

### **1. Add to Profile Setup**
```typescript
// In ProfileSetup component
import YotiAgeVerification from '../components/YotiAgeVerification';

const [showYotiVerification, setShowYotiVerification] = useState(false);

// After DOB validation
if (dob && !util.isAgeBetween18(new Date(), dob)) {
  // User is 18+, show Yoti verification
  setShowYotiVerification(true);
  return;
}

// In render
<YotiAgeVerification
  visible={showYotiVerification}
  onClose={() => setShowYotiVerification(false)}
  onSuccess={() => {
    setShowYotiVerification(false);
    // Proceed to next step
    setCurrentStep(3);
  }}
  onError={(error) => {
    console.error('Yoti verification error:', error);
    Alert.alert('Verification Error', error);
  }}
  userId={user.userInfo.userId}
  minimumAge={18}
/>
```

### **2. Update Component Exports**
```typescript
// In src/components/index.ts
export {default as YotiAgeVerification} from './YotiAgeVerification';
```

## ✅ **Why This Approach Works**

1. **No WebView Issues**: Uses external browser instead of WebView
2. **QR Code Support**: Provides QR code option that works on mobile
3. **Reliable**: Backend polling ensures status updates
4. **User-Friendly**: Clear instructions and multiple options
5. **Secure**: Uses official Yoti APIs and verification flow

## 🔧 **Configuration**

### **Environment Variables**
```env
YOTI_CLIENT_SDK_ID=your_client_sdk_id
YOTI_KEY_FILE_PATH=path/to/your/private-key.pem
YOTI_API_URL=https://api.yoti.com/idverify/v1
BASE_URL=https://your-backend.com
FRONTEND_URL=https://your-frontend.com
```

### **Yoti Hub Setup**
1. Create account at [Yoti Hub](https://hub.yoti.com/)
2. Create Identity Verification application
3. Configure webhook URL: `https://your-backend.com/api/yoti/webhook`
4. Download private key and get Client SDK ID

## 📱 **Testing**

1. **Development**: Use Yoti sandbox environment
2. **QR Code**: Test QR code scanning with Yoti app
3. **Direct Link**: Test direct browser verification
4. **Error Handling**: Test network failures and invalid sessions

This solution provides a robust, mobile-friendly alternative to WebView integration while maintaining the security and reliability of Yoti's age verification system.
